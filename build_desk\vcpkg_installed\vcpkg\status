Package: vcpkg-cmake-config
Version: 2024-05-23
Architecture: x64-windows
Multi-Arch: same
Abi: 8d261494f5cab6cecd7851edc7eb14f27f3c82cd259e8a56095998d1199973a0
Status: install ok installed

Package: vcpkg-cmake
Version: 2024-04-23
Architecture: x64-windows
Multi-Arch: same
Abi: fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
Status: install ok installed

Package: catch2
Version: 3.8.1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: c27ef72cdfe2ca9598d9e45fc77f8646f16dfce66940b3c5779c9b94bc585860
Description: A modern, C++-native, test framework for unit-tests, TDD and BDD.
Status: install ok installed

Package: brotli
Version: 1.1.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: ea8d09d63ff0a6d3fa09cfb577c7c102078a7d15c39770595e1bda9421b58d88
Description: a generic-purpose lossless compression algorithm that compresses data using a combination of a modern variant of the LZ77 algorithm, Huffman coding and 2nd order context modeling.
Status: install ok installed

Package: cpp-httplib
Version: 0.22.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 3295176d96599a43b40bd72158f25f60b0ff1f8c02f8b179de6892156b6126fb
Description: A single file C++11 header-only HTTP/HTTPS server and client library
Default-Features: brotli
Status: install ok installed

Package: cpp-httplib
Feature: brotli
Depends: brotli
Architecture: x64-windows
Multi-Arch: same
Description: Enables brotli compression support using brotli
Status: install ok installed

Package: entt
Version: 3.15.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 75fa4a3a9c3e59522c782925b8223fb0350b31c14971fd91a1856464bcbaf55f
Description: Gaming meets modern C++ - a fast and reliable entity-component system and much more
Status: install ok installed

Package: libwebp
Version: 1.5.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 9dea8edbb6f50d354f612dbadf7296d2adf13758e99411f919e0bbe8ae495da8
Description: WebP codec: library to encode and decode images in WebP format
Default-Features: libwebpmux, nearlossless, simd
Status: install ok installed

Package: libwebp
Feature: libwebpmux
Architecture: x64-windows
Multi-Arch: same
Description: Build the libwebpmux library
Status: install ok installed

Package: libwebp
Feature: nearlossless
Architecture: x64-windows
Multi-Arch: same
Description: Enable near-lossless encoding
Status: install ok installed

Package: libwebp
Feature: simd
Architecture: x64-windows
Multi-Arch: same
Description: Enable any SIMD optimization.
Status: install ok installed

Package: libwebp
Feature: unicode
Architecture: x64-windows
Multi-Arch: same
Description: Build Unicode executables. (Adds definition UNICODE and _UNICODE)
Status: install ok installed

Package: sqlite3
Version: 3.50.2
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 10e4b961eaf0e8e4f81e38d249eda3055c6ab374b4499b027074f64b17cda66b
Description: SQLite is a software library that implements a self-contained, serverless, zero-configuration, transactional SQL database engine.
Default-Features: json1
Status: install ok installed

Package: sqlite3
Feature: json1
Architecture: x64-windows
Multi-Arch: same
Description: Enable JSON functionality for sqlite3
Status: install ok installed

Package: sqlite3
Feature: rtree
Architecture: x64-windows
Multi-Arch: same
Description: Enable the RTREE extension
Status: install ok installed

Package: sqlite3
Feature: tool
Architecture: x64-windows
Multi-Arch: same
Description: Build sqlite3 executable
Status: install ok installed

Package: qhull
Version: 8.0.2
Port-Version: 5
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: bbd6d67180baad38314d0a1e95d8ea7602d9d448a0e2ae39f9b835da762b1d0a
Description: computes the convex hull, Delaunay triangulation, Voronoi diagram
Status: install ok installed

Package: zlib
Version: 1.3.1
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: 435984fbeb96ef7889ac851a04da6ae2211492d5d4d055b103e09923536ad2d8
Description: A compression library
Status: install ok installed

Package: libpng
Version: 1.6.48
Depends: vcpkg-cmake, vcpkg-cmake-config, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: eb802785482d290c987b807233ec7a89a4cc2feafc8898f132e9673a07be073d
Description: libpng is a library implementing an interface for reading and writing PNG (Portable Network Graphics) format files
Status: install ok installed

Package: lerc
Version: 4.0.4
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 84c2671f2dc0318d10366be4fd97255f6d09e9613b1af036542ff581924930ce
Description: An open-source image or raster format which supports rapid encoding and decoding for any pixel type
Status: install ok installed

Package: libjpeg-turbo
Version: 3.1.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 296ca75fcb1eae1dff46c32a7821fd5c366312e98bae969dc521098daf59a53e
Description: libjpeg-turbo is a JPEG image codec that uses SIMD instructions (MMX, SSE2, NEON, AltiVec) to accelerate baseline JPEG compression and decompression on x86, x86-64, ARM, and PowerPC systems.
Status: install ok installed

Package: geos
Version: 3.13.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: c7446fcbaa65dcda4fdcba310e0ff175f25cf7f4b30dbf5a640aec2898dec309
Description: Geometry Engine Open Source
Status: install ok installed

Package: expat
Version: 2.7.1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: b6c4773eb26acc1cc35237a03d8a2dfaab71596f08ff5ae5b7ee9d124baf02c8
Description: XML parser library written in C
Status: install ok installed

Package: curl
Version: 8.14.1
Depends: vcpkg-cmake, vcpkg-cmake-config, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: 6e318470fab613f7470c8c523682777365a86a2be21fabe01719c9c79a268559
Description: A library for transferring data with URLs
Default-Features: non-http, ssl
Status: install ok installed

Package: curl
Feature: non-http
Architecture: x64-windows
Multi-Arch: same
Description: Enables protocols beyond HTTP/HTTPS/HTTP2
Status: install ok installed

Package: curl
Feature: schannel
Architecture: x64-windows
Multi-Arch: same
Description: SSL support (Secure Channel)
Status: install ok installed

Package: curl
Feature: ssl
Architecture: x64-windows
Multi-Arch: same
Description: Default SSL backend
Status: install ok installed

Package: curl
Feature: sspi
Architecture: x64-windows
Multi-Arch: same
Description: SSPI support
Status: install ok installed

Package: vcpkg-cmake-get-vars
Version: 2025-05-29
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: 5b9c6c71e45a0caaecd95d58aa8ebb05fdf46c1f4e2d8eb838ae0730e64bb7e4
Status: install ok installed

Package: vcpkg-tool-meson
Version: 1.8.2
Depends: vcpkg-cmake-get-vars
Architecture: x64-windows
Multi-Arch: same
Abi: e5bf6cfafcbfa4261cf4b2519ff9531db17cb91bb1e0bc62088c48e5e5e5afea
Description: Meson build system
Status: install ok installed

Package: pkgconf
Version: 2.4.3
Port-Version: 1
Depends: vcpkg-tool-meson
Architecture: x64-windows
Multi-Arch: same
Abi: d2de46e042cc0bdbbae7e3adcd3a38ca57d2b188cca2d86bd998c911374fad31
Description: pkgconf is a program which helps to configure compiler and linker flags for development libraries. It is similar to pkg-config from freedesktop.org.
Status: install ok installed

Package: vcpkg-pkgconfig-get-modules
Version: 2024-04-03
Depends: pkgconf
Architecture: x64-windows
Multi-Arch: same
Abi: c349fed9d0cb2df9a88b897024146b347ae36970a7430627b78e4c0a2c705e7e
Status: install ok installed

Package: liblzma
Version: 5.8.1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: e7f2ac12b18b7696dff8dd54fb3f50093d7f6e7a5846d7d7555ad1a1b8e8e08f
Description: Compression library with an API similar to that of zlib.
Status: install ok installed

Package: tiff
Version: 4.7.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 0e53014587649b3b8a7371efe32feeedf1d848ae7fe6bbec6afad7d403399c6c
Description: A library that supports the manipulation of TIFF image files
Default-Features: jpeg, lzma, zip
Status: install ok installed

Package: tiff
Feature: jpeg
Depends: libjpeg-turbo
Architecture: x64-windows
Multi-Arch: same
Description: Support JPEG compression in TIFF image files
Status: install ok installed

Package: tiff
Feature: lzma
Depends: liblzma
Architecture: x64-windows
Multi-Arch: same
Description: Support LZMA compression in TIFF image files
Status: install ok installed

Package: tiff
Feature: zip
Depends: zlib
Architecture: x64-windows
Multi-Arch: same
Description: Support ZIP/deflate compression in TIFF image files
Status: install ok installed

Package: nlohmann-json
Version: 3.12.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: c4a859510564d1a1833a7fb3e7315cfad11f3db013b1dd215af6da8714a34cc9
Description: JSON for Modern C++
Status: install ok installed

Package: proj
Version: 9.6.2
Depends: nlohmann-json, sqlite3, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: f7c8fd0d70aadf30ba0a0377326ffa204a7a7fe2fe634e58c121c2d49344fb24
Description: PROJ library for cartographic projections
Default-Features: net, tiff
Status: install ok installed

Package: proj
Feature: net
Depends: curl
Architecture: x64-windows
Multi-Arch: same
Description: Enable network support
Status: install ok installed

Package: proj
Feature: tiff
Depends: tiff
Architecture: x64-windows
Multi-Arch: same
Description: Enable TIFF support to read some grids
Status: install ok installed

Package: libgeotiff
Version: 1.7.4
Depends: proj, tiff, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 6ffba80938929d9e3796c92e2ecf45443d73dc223ed7b555748bced1ccd38963
Description: Libgeotiff is an open source library on top of libtiff for reading and writing GeoTIFF information tags.
Status: install ok installed

Package: json-c
Version: 0.18-20240915
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 3fbb223e664052d491a9d79482552debded650cd881458bf94e581ab11c9d99d
Description: A JSON implementation in C
Status: install ok installed

Package: gdal
Version: 3.11.0
Port-Version: 1
Depends: json-c, libgeotiff, pkgconf, proj, tiff, vcpkg-cmake, vcpkg-cmake-config, vcpkg-pkgconfig-get-modules, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: 8bf4fc73dd14ed101c57b3824cceba323b48cb46df0538e8d330c5c173043a09
Description: The Geographic Data Abstraction Library for reading and writing geospatial raster and vector data
Default-Features: gif, hdf5, iconv, libkml, libspatialite, libxml2, lzma, netcdf, openjpeg, openssl, pcre2, postgresql, recommended-features, webp, zstd
Status: install ok installed

Package: gdal
Feature: curl
Depends: curl
Architecture: x64-windows
Multi-Arch: same
Description: Enable CURL network support
Status: install ok installed

Package: gdal
Feature: expat
Depends: expat
Architecture: x64-windows
Multi-Arch: same
Description: Use EXPAT library
Status: install ok installed

Package: gdal
Feature: geos
Depends: geos
Architecture: x64-windows
Multi-Arch: same
Description: Enable GEOS support
Status: install ok installed

Package: gdal
Feature: jpeg
Depends: libjpeg-turbo
Architecture: x64-windows
Multi-Arch: same
Description: Use JPEG compression library
Status: install ok installed

Package: gdal
Feature: lerc
Depends: lerc
Architecture: x64-windows
Multi-Arch: same
Description: Enable LERC support
Status: install ok installed

Package: gdal
Feature: png
Depends: libpng
Architecture: x64-windows
Multi-Arch: same
Description: Use PNG compression library
Status: install ok installed

Package: gdal
Feature: qhull
Depends: qhull
Architecture: x64-windows
Multi-Arch: same
Description: Use QHULL library
Status: install ok installed

Package: gdal
Feature: recommended-features
Architecture: x64-windows
Multi-Arch: same
Description: Features that are explicity marked as recommended by GDAL.
Status: install ok installed

Package: gdal
Feature: sqlite3
Depends: sqlite3
Architecture: x64-windows
Multi-Arch: same
Description: Enable SQLite3 support
Status: install ok installed

Package: gdal
Feature: webp
Depends: libwebp
Architecture: x64-windows
Multi-Arch: same
Description: Enable WEBP support
Status: install ok installed

Package: glm
Version: 1.0.1
Port-Version: 3
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 41cce11045a58e64c11d8d85fb9ae7208f699c0cb8f7c9e5b698143f7c5c1e28
Description: OpenGL Mathematics (GLM)
Status: install ok installed

Package: vulkan-headers
Version: 1.4.309.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: deedaece2daa424419521aaef2c9916ebf5fd8fa7aea63f1696d6c062420d817
Description: Vulkan header files and API registry
Status: install ok installed

Package: vulkan-loader
Version: 1.4.309.0
Depends: vcpkg-cmake, vcpkg-cmake-config, vulkan-headers
Architecture: x64-windows
Multi-Arch: same
Abi: 10bdc591bbdea1eda050cf3e1711ef24c9987a5d037fda79892d7a6d09a72c9d
Description: Vulkan Development Tools
Status: install ok installed

Package: vulkan
Version: 2023-12-17
Depends: vcpkg-cmake, vulkan-headers, vulkan-loader
Architecture: x64-windows
Multi-Arch: same
Abi: 2feb6e4d61f58ed7f5aa69a5916d8660986e5ad36147ba0391af3f59a3c6f9ea
Description: A stub package that ensures that Vulkan headers and a loader are available.
    On Android, the NDK provides a loader at API level 24 or higher.
Status: install ok installed

Package: imgui
Version: 1.91.9
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 773e7476534528131b6564c557f0f3367396ba3d0d80abfa56f97e7404722aec
Description: Bloat-free Immediate Mode Graphical User interface for C++ with minimal dependencies.
Status: install ok installed

Package: imgui
Feature: vulkan-binding
Depends: vulkan
Architecture: x64-windows
Multi-Arch: same
Description: Make available Vulkan binding
Status: install ok installed

Package: openssl
Version: 3.5.0
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config, vcpkg-cmake-get-vars
Architecture: x64-windows
Multi-Arch: same
Abi: a6620427596fa5463ff22f490a7b0615924bf9a036dcaf2198fdcbedcd2e9623
Description: OpenSSL is an open source project that provides a robust, commercial-grade, and full-featured toolkit for the Transport Layer Security (TLS) and Secure Sockets Layer (SSL) protocols. It is also a general-purpose cryptography library.
Status: install ok installed

Package: fmt
Version: 11.0.2
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 424bd90e190980d37ab2b51ebbac4ceeb43fe95b50997d46ec42047418f53fbd
Description: {fmt} is an open-source formatting library providing a fast and safe alternative to C stdio and C++ iostreams.
Status: install ok installed

Package: spdlog
Version: 1.15.3
Depends: fmt, vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 18dbe19575e5c2f8105d56cd473c62eabfb38ac5091f3289af23e9c485316681
Description: Very fast, header-only/compiled, C++ logging library.
Status: install ok installed

Package: glslang
Version: 15.1.0
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: ffffb966d5bb41f29e1680b4bc4194781fe2b8fa15b03c98d3dbd5ece75bf187
Description: Khronos-reference front end for GLSL/ESSL, partial front end for HLSL, and a SPIR-V generator.
Status: install ok installed

Package: vsg
Version: 1.1.10
Depends: glslang, vcpkg-cmake, vcpkg-cmake-config, vulkan
Architecture: x64-windows
Multi-Arch: same
Abi: 75ed1d1386640fde06416b48f8e974d469ae80b9e1944318026979955a2f483c
Description: A modern, cross platform, high performance scene graph library built upon Vulkan.
Status: install ok installed

Package: bzip2
Version: 1.0.8
Port-Version: 6
Depends: vcpkg-cmake
Architecture: x64-windows
Multi-Arch: same
Abi: e6a1d2088f7f4359cf05733e9cd5da5602eb60ff1083444aca8c69f55f09b46d
Description: bzip2 is a freely available, patent free, high-quality data compressor. It typically compresses files to within 10% to 15% of the best available techniques (the PPM family of statistical compressors), whilst being around twice as fast at compression and six times faster at decompression.
Default-Features: tool
Status: install ok installed

Package: bzip2
Feature: tool
Architecture: x64-windows
Multi-Arch: same
Description: Builds bzip2 executable
Status: install ok installed

Package: freetype
Version: 2.13.3
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: d567993976f4cb44c08b2e5a53c829754d97209871617290641b45af1aafaade
Description: A library to render fonts.
Default-Features: brotli, bzip2, png, zlib
Status: install ok installed

Package: freetype
Feature: brotli
Depends: brotli
Architecture: x64-windows
Multi-Arch: same
Description: Support decompression of WOFF2 streams
Status: install ok installed

Package: freetype
Feature: bzip2
Depends: bzip2
Architecture: x64-windows
Multi-Arch: same
Description: Support bzip2 compressed fonts.
Status: install ok installed

Package: freetype
Feature: png
Depends: libpng
Architecture: x64-windows
Multi-Arch: same
Description: Support PNG compressed OpenType embedded bitmaps.
Status: install ok installed

Package: freetype
Feature: zlib
Depends: zlib
Architecture: x64-windows
Multi-Arch: same
Description: Use zlib instead of internal library for DEFLATE
Status: install ok installed

Package: utfcpp
Version: 4.0.6
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 86ee82ded8dfc167a7930699e60e157b3bce60d75a8e84ae867a2ea9385aedb1
Description: UTF-8 with C++ in a Portable Way
Status: install ok installed

Package: stb
Version: 2024-07-29
Port-Version: 1
Architecture: x64-windows
Multi-Arch: same
Abi: 85cb678c297d6bbb15ac0021a0e92a054e1eb9d52c6975c41be451b10f1fe1d7
Description: public domain header-only libraries
Status: install ok installed

Package: rapidjson
Version: 2025-02-26
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 997ff4aaf2fa8128ed8dd50b3fb2d4e4a22711fa7033bceb3436d4246829b807
Description: A fast JSON parser/generator for C++ with both SAX/DOM style API <http://rapidjson.org/>
Status: install ok installed

Package: pugixml
Version: 1.15
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: afcab612c79e4f4b2be7bfa562d7c6be146720c9de0d8a134ef798117864ce52
Description: Light-weight, simple and fast XML parser for C++ with XPath support
Status: install ok installed

Package: polyclipping
Version: 6.4.2
Port-Version: 13
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 770a64d59dba24dc0d4a44f44374f49fffe678d16cfbb982e38bc6eb3bba2467
Description: The Clipper library performs clipping and offsetting for both lines and polygons. All four boolean clipping operations are supported - intersection, union, difference and exclusive-or. Polygons can be of any shape including self-intersecting polygons.
Status: install ok installed

Package: minizip
Version: 1.3.1
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config, vcpkg-cmake-get-vars, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: 09250874e9312e41a0b5ffbcfbc60296faf5074a355bbfd9bf0d13ed31355b83
Description: Minizip zip file manipulation library
Status: install ok installed

Package: kubazip
Version: 0.3.4
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 0e0349f7ccc550029381a619eb9bb35d6b99796226517058dc0843e43c339e4d
Description: A portable, simple zip library written in C
Status: install ok installed

Package: jhasse-poly2tri
Version: 2023-12-27
Port-Version: 2
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 81ad1decf56e06b364eb5b29271706d8c94837b41eaa5d684e054e4b478b8772
Description: Sweep-line algorithm for constrained Delaunay triangulation
Status: install ok installed

Package: draco
Version: 1.5.7
Depends: vcpkg-cmake, vcpkg-cmake-config
Architecture: x64-windows
Multi-Arch: same
Abi: 24899da6f9418adc36c917b2e372ea244bb922b6bddb818422158182ad377eea
Description: A library for compressing and decompressing 3D geometric meshes and point clouds. It is intended to improve the storage and transmission of 3D graphics.
Status: install ok installed

Package: assimp
Version: 6.0.2
Port-Version: 1
Depends: draco, jhasse-poly2tri, kubazip, minizip, polyclipping, pugixml, rapidjson, stb, utfcpp, vcpkg-cmake, vcpkg-cmake-config, zlib
Architecture: x64-windows
Multi-Arch: same
Abi: 9025f0be1367779b748de6a6908e3d83c019994c14184fd00fb6a114a517b771
Description: The Open Asset import library
Status: install ok installed

Package: vsgxchange
Version: 1.1.4
Port-Version: 1
Depends: vcpkg-cmake, vcpkg-cmake-config, vsg
Architecture: x64-windows
Multi-Arch: same
Abi: a5b00178c92ae304b8539570ee175898c37d6e3f96776b82be05c32ddefc5c6f
Description: Utility library for converting 3rd party images, models and fonts formats to/from VulkanSceneGraph.
Status: install ok installed

Package: vsgxchange
Feature: assimp
Depends: assimp
Architecture: x64-windows
Multi-Arch: same
Description: Enable support for reading 3D model formats as vsg::Node via Assimp
Status: install ok installed

Package: vsgxchange
Feature: freetype
Depends: freetype
Architecture: x64-windows
Multi-Arch: same
Description: Enable support for reading fonts as vsg::Font via Freetype
Status: install ok installed

