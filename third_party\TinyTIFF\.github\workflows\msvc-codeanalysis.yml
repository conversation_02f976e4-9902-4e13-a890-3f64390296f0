name: "MSVC-CodeAnalysis"

on: [push, pull_request]

  
env:
  # Path to the CMake build directory.
  build: '${{ github.workspace }}/build'
  config: 'Debug'

jobs:
  analyze:
    name: Analyze
    runs-on: windows-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
          
      - name: Configure CMake
        run: cmake -B ${{ env.build }} -DCMAKE_BUILD_TYPE=${{ env.config }} "-DTinyTIFF_BUILD_SHARED_LIBS:BOOL=OON" "-DTinyTIFF_BUILD_STATIC_LIBS:BOOL=ON" "-DBUILD_SHARED_LIBS:BOOL=ON" "-DTinyTIFF_BUILD_TESTS:BOOL=OFF"

      - name: Build CMake
        run: cmake --build ${{ env.build }} --config ${{ env.config }}

      - name: Run MSVC Code Analysis
        uses: microsoft/msvc-code-analysis-action@v0.1.1
        # Provide a unique ID to access the sarif output path
        id: run-analysis
        with:
          cmakeBuildDirectory: ${{ env.build }}
          buildConfiguration: ${{ env.config }}
          # Ruleset file that will determine what checks will be run
          ruleset: NativeRecommendedRules.ruleset
          # Paths to ignore analysis of CMake targets and includes
          # ignoredPaths: ${{ github.workspace }}/dependencies;${{ github.workspace }}/test

      # Upload SARIF file to GitHub Code Scanning Alerts
      - name: Upload SARIF to GitHub
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: ${{ steps.run-analysis.outputs.sarif }}

      # Upload SARIF file as an Artifact to download and view
      - name: Upload SARIF as an Artifact
        uses: actions/upload-artifact@v2
        with:
          name: sarif-file
          path: ${{ steps.run-analysis.outputs.sarif }}

          
