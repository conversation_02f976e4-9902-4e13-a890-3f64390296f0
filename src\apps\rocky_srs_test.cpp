#include <rocky/SRS.h>
#include <rocky/GeoPoint.h>
#include <rocky/GeographicLibAdapter.h>
#include <iostream>
#include <iomanip>
#include <vector>

using namespace rocky;

void printHeader(const std::string& title) {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "  " << title << std::endl;
    std::cout << std::string(60, '=') << std::endl;
}

void testBasicSRS() {
    printHeader("基本坐标系测试");
    
    // 测试WGS84
    SRS wgs84("wgs84");
    std::cout << "WGS84坐标系: " << (wgs84.valid() ? "✓ 有效" : "✗ 无效") << std::endl;
    if (wgs84.valid()) {
        std::cout << "  名称: " << wgs84.name() << std::endl;
        std::cout << "  是否为地理坐标系: " << (wgs84.isGeodetic() ? "是" : "否") << std::endl;
    }
    
    // 测试Web Mercator
    SRS webMercator("spherical-mercator");
    std::cout << "Web Mercator坐标系: " << (webMercator.valid() ? "✓ 有效" : "✗ 无效") << std::endl;
    if (webMercator.valid()) {
        std::cout << "  名称: " << webMercator.name() << std::endl;
        std::cout << "  是否为投影坐标系: " << (webMercator.isProjected() ? "是" : "否") << std::endl;
    }
    
    // 测试地心坐标系
    SRS ecef("geocentric");
    std::cout << "地心坐标系: " << (ecef.valid() ? "✓ 有效" : "✗ 无效") << std::endl;
    if (ecef.valid()) {
        std::cout << "  名称: " << ecef.name() << std::endl;
        std::cout << "  是否为地心坐标系: " << (ecef.isGeocentric() ? "是" : "否") << std::endl;
    }
}

void testEPSGCodes() {
    printHeader("EPSG代码测试");
    
    std::vector<std::string> epsgCodes = {
        "epsg:4326",  // WGS84
        "epsg:3857",  // Web Mercator
        "epsg:4978"   // WGS84 Geocentric
    };
    
    for (const auto& code : epsgCodes) {
        SRS srs(code);
        std::cout << code << ": " << (srs.valid() ? "✓ 有效" : "✗ 无效");
        if (srs.valid()) {
            std::cout << " - " << srs.name();
        }
        std::cout << std::endl;
    }
}

void testCoordinateTransformation() {
    printHeader("坐标转换测试");
    
    SRS wgs84("wgs84");
    SRS webMercator("spherical-mercator");
    
    if (!wgs84.valid() || !webMercator.valid()) {
        std::cout << "坐标系无效，跳过转换测试" << std::endl;
        return;
    }
    
    // 测试几个著名城市的坐标转换
    struct CityCoord {
        std::string name;
        double lon, lat;
    };
    
    std::vector<CityCoord> cities = {
        {"北京", 116.4074, 39.9042},
        {"上海", 121.4737, 31.2304},
        {"广州", 113.2644, 23.1291},
        {"深圳", 114.0579, 22.5431},
        {"伦敦", -0.1276, 51.5074},
        {"纽约", -74.0060, 40.7128}
    };
    
    std::cout << std::fixed << std::setprecision(6);
    
    for (const auto& city : cities) {
        std::cout << "\n" << city.name << ":" << std::endl;
        std::cout << "  WGS84: (" << city.lon << ", " << city.lat << ")" << std::endl;
        
        try {
            GeoPoint wgsPoint(wgs84, city.lon, city.lat, 0.0);
            GeoPoint mercatorPoint = wgsPoint.transform(webMercator);
            
            if (mercatorPoint.srs.valid()) {
                std::cout << "  Web Mercator: (" << mercatorPoint.x << ", " << mercatorPoint.y << ")" << std::endl;
                
                // 反向转换验证
                GeoPoint backToWgs = mercatorPoint.transform(wgs84);
                if (backToWgs.srs.valid()) {
                    double lonDiff = std::abs(backToWgs.x - city.lon);
                    double latDiff = std::abs(backToWgs.y - city.lat);
                    std::cout << "  反向转换: (" << backToWgs.x << ", " << backToWgs.y << ")";
                    if (lonDiff < 0.000001 && latDiff < 0.000001) {
                        std::cout << " ✓ 精度验证通过" << std::endl;
                    } else {
                        std::cout << " ⚠ 精度误差: Δlon=" << lonDiff << ", Δlat=" << latDiff << std::endl;
                    }
                }
            } else {
                std::cout << "  ✗ 转换失败" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "  ✗ 转换异常: " << e.what() << std::endl;
        }
    }
}

void testEllipsoidParameters() {
    printHeader("椭球体参数测试");
    
    SRS wgs84("wgs84");
    if (wgs84.valid()) {
        const Ellipsoid& ellipsoid = wgs84.ellipsoid();
        std::cout << "WGS84椭球体参数:" << std::endl;
        std::cout << "  长半轴: " << std::fixed << std::setprecision(3) << ellipsoid.semiMajorAxis() << " 米" << std::endl;
        std::cout << "  短半轴: " << std::fixed << std::setprecision(3) << ellipsoid.semiMinorAxis() << " 米" << std::endl;
        std::cout << "  扁率: " << std::scientific << std::setprecision(10) << ellipsoid.flattening() << std::endl;
    }
}

void testVersionInfo() {
    printHeader("版本信息");
    
    std::cout << "Rocky SRS版本: " << SRS::projVersion() << std::endl;
    std::cout << "GeographicLib适配器版本: " << GeographicLibAdapter::version() << std::endl;
}

int main() {
    std::cout << "Rocky 坐标转换系统测试程序" << std::endl;
    std::cout << "基于GeographicLib适配器实现" << std::endl;
    
    try {
        testVersionInfo();
        testBasicSRS();
        testEPSGCodes();
        testCoordinateTransformation();
        testEllipsoidParameters();
        
        printHeader("测试完成");
        std::cout << "✓ 所有测试已完成" << std::endl;
        std::cout << "✓ Rocky SRS重构成功，PROJ库已替换为GeographicLib适配器" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ 测试过程中发生异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
