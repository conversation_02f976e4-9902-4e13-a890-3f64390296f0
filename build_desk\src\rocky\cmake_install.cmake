# Install script for directory: F:/cmo-dev/my_osgearth_web/rocky/src/rocky

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/Program Files (x86)/rocky")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Release")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/rocky/rocky-targets.cmake")
    file(DIFFERENT _cmake_export_file_changed FILES
         "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/rocky/rocky-targets.cmake"
         "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/CMakeFiles/Export/765d96849f963fdd41fc35c41750e2d4/rocky-targets.cmake")
    if(_cmake_export_file_changed)
      file(GLOB _cmake_old_config_files "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/rocky/rocky-targets-*.cmake")
      if(_cmake_old_config_files)
        string(REPLACE ";" ", " _cmake_old_config_files_text "${_cmake_old_config_files}")
        message(STATUS "Old export file \"$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/rocky/rocky-targets.cmake\" will be replaced.  Removing files [${_cmake_old_config_files_text}].")
        unset(_cmake_old_config_files_text)
        file(REMOVE ${_cmake_old_config_files})
      endif()
      unset(_cmake_old_config_files)
    endif()
    unset(_cmake_export_file_changed)
  endif()
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake/rocky" TYPE FILE FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/CMakeFiles/Export/765d96849f963fdd41fc35c41750e2d4/rocky-targets.cmake")
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake/rocky" TYPE FILE FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/CMakeFiles/Export/765d96849f963fdd41fc35c41750e2d4/rocky-targets-debug.cmake")
  endif()
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake/rocky" TYPE FILE FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/CMakeFiles/Export/765d96849f963fdd41fc35c41750e2d4/rocky-targets-minsizerel.cmake")
  endif()
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake/rocky" TYPE FILE FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/CMakeFiles/Export/765d96849f963fdd41fc35c41750e2d4/rocky-targets-relwithdebinfo.cmake")
  endif()
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake/rocky" TYPE FILE FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/CMakeFiles/Export/765d96849f963fdd41fc35c41750e2d4/rocky-targets-release.cmake")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake/rocky" TYPE FILE FILES
    "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/rocky-config.cmake"
    "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/rocky-config-version.cmake"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY OPTIONAL FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/Debug/rocky.lib")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY OPTIONAL FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/Release/rocky.lib")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY OPTIONAL FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/MinSizeRel/rocky.lib")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY OPTIONAL FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/RelWithDebInfo/rocky.lib")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE SHARED_LIBRARY FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/Debug/rocky.dll")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ee][Aa][Ss][Ee])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE SHARED_LIBRARY FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/Release/rocky.dll")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Mm][Ii][Nn][Ss][Ii][Zz][Ee][Rr][Ee][Ll])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE SHARED_LIBRARY FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/MinSizeRel/rocky.dll")
  elseif(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Rr][Ee][Ll][Ww][Ii][Tt][Hh][Dd][Ee][Bb][Ii][Nn][Ff][Oo])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bin" TYPE SHARED_LIBRARY FILES "F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/RelWithDebInfo/rocky.dll")
  endif()
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/rocky" TYPE FILE FILES
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Azure.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/AzureImageLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Bing.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/BingElevationLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/BingImageLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Callbacks.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Color.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Common.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Config.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Context.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/DateTime.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/ElevationLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Ellipsoid.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Ephemeris.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Feature.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/GeoCircle.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/GeoCommon.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/GeoExtent.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/GeoHeightfield.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/GeoImage.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/GeoPoint.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Geocoder.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/GeographicLibAdapter.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Geoid.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Heightfield.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Horizon.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/IOTypes.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Image.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/ImageLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/LRUCache.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Layer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/LayerCollection.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/LayerReference.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/LibTIFFReader.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Log.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/MBTiles.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/MBTilesElevationLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/MBTilesImageLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Map.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Math.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Memory.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Profile.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/SRS.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/SentryTracker.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Status.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/TMS.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/TMSElevationLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/TMSImageLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/TerrainTileModel.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/TerrainTileModelFactory.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Threading.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/TileKey.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/TileLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/URI.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Units.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Utils.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Viewpoint.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/VisibleLayer.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/json.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/option.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/rocky.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/rtree.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/sha1.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/tinyxml/tinyxml.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/weejobs.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/weemesh.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/Version.h.in"
    "F:/cmo-dev/my_osgearth_web/rocky/build_desk/build_include/rocky/Version.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/LibTIFFReader.h"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/rocky/contrib" TYPE FILE FILES "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/contrib/EarthFileImporter.h")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/share/rocky/data" TYPE FILE FILES "F:/cmo-dev/my_osgearth_web/rocky/data/fonts/times.vsgb")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/rocky/vsg" TYPE FILE FILES
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/Application.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/Common.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/DisplayManager.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/GeoTransform.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/MapManipulator.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/MapNode.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/NodePager.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/PipelineState.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/PixelScaleTransform.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/RTT.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/SkyNode.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/Utils.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/VSGContext.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ViewLocal.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/json.h"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/rocky/vsg/terrain" TYPE FILE FILES
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/terrain/GeometryPool.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/terrain/SurfaceNode.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/terrain/TerrainEngine.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/terrain/TerrainNode.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/terrain/TerrainSettings.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/terrain/TerrainState.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/terrain/TerrainTileHost.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/terrain/TerrainTileNode.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/terrain/TerrainTilePager.h"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/rocky/vsg/ecs" TYPE FILE FILES
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Component.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Declutter.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/ECSNode.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/FeatureView.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Icon.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/IconSystem.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/IconSystem2.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Label.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/LabelSystem.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Line.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/LineSystem.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Mesh.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/MeshSystem.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Motion.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/MotionSystem.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Registry.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Transform.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/TransformDetail.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/TransformSystem.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Visibility.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/Widget.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/ecs/WidgetSystem.h"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/rocky/vsg/imgui" TYPE FILE FILES
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/imgui/ImGuiIntegration.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/imgui/RenderImGui.h"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/imgui/SendEventsToImGui.h"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/share/rocky/shaders" TYPE FILE FILES
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.atmo.ground.vert.glsl"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.atmo.sky.frag"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.atmo.sky.vert"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.icon.frag"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.icon.indirect.cull.comp"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.icon.indirect.frag"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.icon.indirect.vert"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.icon.vert"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.lighting.frag.glsl"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.line.frag"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.line.vert"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.mesh.frag"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.mesh.vert"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.terrain.frag"
    "F:/cmo-dev/my_osgearth_web/rocky/src/rocky/vsg/shaders/rocky.terrain.vert"
    )
endif()

