/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#include "TMSElevationLayer.h"
#include "Context.h"
#include "json.h"

using namespace ROCKY_NAMESPACE;
using namespace ROCKY_NAMESPACE::TMS;

#undef LC
#define LC "[TMS] "

namespace
{
    /**
     * 解码16位TIFF高程数据 (如ReadyMap格式)
     * 使用正确的转换公式: elevation = raw_value / 100
     */
    std::shared_ptr<Heightfield> decode16BitTIFF(std::shared_ptr<Image> image)
    {
        if (!image || image->pixelFormat() != Image::R16_UNORM)
            return nullptr;

        auto hf = Heightfield::create(image->width(), image->height());
        if (!hf)
            return nullptr;

        const uint16_t *src = image->data<uint16_t>();

        for (int y = 0; y < image->height(); ++y)
        {
            for (int x = 0; x < image->width(); ++x)
            {
                uint16_t rawValue = src[y * image->width() + x];

                float elevation;

                // 检查NoData值
                if (rawValue == 0 || rawValue == 65535)
                {
                    elevation = NO_DATA_VALUE;
                }
                else
                {
                    // ReadyMap的正确转换公式 (经过验证)
                    elevation = (float)rawValue / 100.0f;
                }

                hf->heightAt(x, y) = elevation;
            }
        }

        Log()->info(LC "Decoded 16-bit TIFF elevation data using formula: elevation = raw_value / 100");
        return hf;
    }
}

ROCKY_ADD_OBJECT_FACTORY(TMSElevation,
                         [](const std::string &JSON, const IOOptions &io)
                         { return TMSElevationLayer::create(JSON, io); })

TMSElevationLayer::TMSElevationLayer() : super()
{
    construct({}, {});
}

TMSElevationLayer::TMSElevationLayer(const std::string &JSON, const IOOptions &io) : super(JSON, io)
{
    construct(JSON, io);
}

void TMSElevationLayer::construct(const std::string &JSON, const IOOptions &io)
{
    setLayerTypeName("TMSElevation");
    const auto j = parse_json(JSON);
    get_to(j, "uri", uri, io);
    get_to(j, "format", format);
    get_to(j, "invert_y", invertY);
}

JSON TMSElevationLayer::to_json() const
{
    auto j = parse_json(super::to_json());
    set(j, "uri", uri);
    set(j, "format", format);
    set(j, "invert_y", invertY);
    return j.dump();
}

Status
TMSElevationLayer::openImplementation(const IOOptions &io)
{
    Status parent = super::openImplementation(io);
    if (parent.failed())
        return parent;

    Profile driver_profile = profile;

    DataExtentList dataExtents;
    Status status = _driver.open(
        uri,
        driver_profile,
        format,
        dataExtents,
        io);

    if (status.failed())
        return status;

    if (driver_profile != profile)
    {
        profile = driver_profile;
    }

    // If the layer name is unset, try to set it from the tileMap title.
    if (name().empty() && !_driver.tileMap.title.empty())
    {
        setName(_driver.tileMap.title);
    }

    setDataExtents(dataExtents);

    return StatusOK;
}

void TMSElevationLayer::closeImplementation()
{
    _driver.close();
    super::closeImplementation();
}

Result<GeoHeightfield>
TMSElevationLayer::createHeightfieldImplementation(const TileKey &key, const IOOptions &io) const
{
    if (!isOpen())
        return status();

    // request
    auto r = _driver.read(key, invertY, encoding == Encoding::MapboxRGB, uri->context(), io);

    if (r.status.ok())
    {
        if (r.value->pixelFormat() == Image::R32_SFLOAT)
        {
            return GeoHeightfield(Heightfield::create(r.value.get()), key.extent());
        }
        else if (r.value->pixelFormat() == Image::R16_UNORM)
        {
            // Handle 16-bit TIFF elevation data (like ReadyMap)
            auto hf = decode16BitTIFF(r.value);
            return GeoHeightfield(hf, key.extent());
        }
        else // assume Image::R8G8B8_UNORM (RGB elevation encoding)
        {
            auto hf = decodeRGB(r.value);
            return GeoHeightfield(hf, key.extent());
        }
    }
    else
    {
        if (r.status.code == Status::ServiceUnavailable)
        {
            setStatus(r.status);
            Log()->warn(LC "Layer \"" + name() + "\" : " + r.status.message);
        }

        return r.status;
    }
}
