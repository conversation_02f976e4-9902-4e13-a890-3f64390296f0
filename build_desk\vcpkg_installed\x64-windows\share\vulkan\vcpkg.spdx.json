{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/vulkan-x64-windows-2023-12-17-f36294cd-59a4-4077-9d4e-995f5bc259d9", "name": "vulkan:x64-windows@2023-12-17 2feb6e4d61f58ed7f5aa69a5916d8660986e5ad36147ba0391af3f59a3c6f9ea", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-02T08:41:35Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-3"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-4"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-3", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-4", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "vulkan", "SPDXID": "SPDXRef-port", "versionInfo": "2023-12-17", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/vulkan", "licenseConcluded": "LicenseRef-vcpkg-null", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "A stub package that ensures that Vulkan headers and a loader are available.\nOn Android, the NDK provides a loader at API level 24 or higher.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "vulkan:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "2feb6e4d61f58ed7f5aa69a5916d8660986e5ad36147ba0391af3f59a3c6f9ea", "downloadLocation": "NONE", "licenseConcluded": "LicenseRef-vcpkg-null", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}], "files": [{"fileName": "./CMakeLists.txt", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "0724cb6096811adb0e277dbf521e8b96508bcc3059d97fdda75f705e6e8f0baa"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "f8ec31ffb8c8cf5e227947b604ae31d394b70a982fc02e87cfe917efe518a0dc"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./usage", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "c3642e7a8674d00cc7c6bde2484e83f75d29fbdd356d8275cf55a6067c7986ea"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-3", "checksums": [{"algorithm": "SHA256", "checksumValue": "6ab4a4ea9f8ea235bb4f9dd10a431ffbb6f229fdb7cbd1ed94550ffa3e2de667"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vulkan-result.cmake.in", "SPDXID": "SPDXRef-file-4", "checksums": [{"algorithm": "SHA256", "checksumValue": "25d969806cf403e575977881b4db44801a0e9fcda860e87449efed4398912442"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}