{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/utfcpp-x64-windows-4.0.6-57d23095-03fe-4694-9f24-58ccf295aaa6", "name": "utfcpp:x64-windows@4.0.6 86ee82ded8dfc167a7930699e60e157b3bce60d75a8e84ae867a2ea9385aedb1", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-02T09:14:07Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "utfcpp", "SPDXID": "SPDXRef-port", "versionInfo": "4.0.6", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/utfcpp", "homepage": "https://github.com/nemtrif/utfcpp", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "UTF-8 with C++ in a Portable Way", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "utfcpp:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "86ee82ded8dfc167a7930699e60e157b3bce60d75a8e84ae867a2ea9385aedb1", "downloadLocation": "NONE", "licenseConcluded": "BSL-1.0", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "nemtrif/utfcpp", "downloadLocation": "git+https://github.com/nemtrif/utfcpp@v4.0.6", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "53c59f2e04fe5d36faf98a238b94f774834a34982d481a8170ee144f7f8c2d4ba249a732d90654922944c1075c578690c327091883398c533d604bf49f4a6ecf"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "3d1e0695295932dc8f4145cfd0211bb5d8083063d86294a3bdcba4d45d370c03"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "f9fc4ecf0faadd3d2f567b66495d9482f16c645abe29b9689154031426a20c94"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}