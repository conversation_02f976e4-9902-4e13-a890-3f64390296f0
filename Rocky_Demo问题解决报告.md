# Rocky Demo 问题解决报告

## 问题描述

用户报告了两个主要问题：
1. 点击run_rocky_demo.bat弹出两个窗口，其中一个完全是黑的
2. 窗口中只有ImGui界面，没有数字地球，也没有显示谷歌地球的瓦片

## 问题分析与解决过程

### 问题1: 双窗口问题 ✅ 已解决

**原因分析:**
- rocky_demo.cpp中调用了`app.realize()`创建默认窗口
- 然后又手动创建了另一个窗口：`auto main_window = app.displayManager->addWindow(traits)`
- 导致出现两个窗口

**解决方案:**
修改rocky_demo.cpp，使用默认窗口而不是创建新窗口：

```cpp
// 修改前：手动创建新窗口
auto traits = vsg::WindowTraits::create(1920, 1080, "Main Window");
auto main_window = app.displayManager->addWindow(traits);

// 修改后：使用默认窗口
if (app.displayManager && !app.displayManager->windowsAndViews.empty())
{
    auto main_window = app.displayManager->windowsAndViews.begin()->first;
    // ... ImGui设置
}
```

### 问题2: 着色器缺失问题 ✅ 已解决

**原因分析:**
- 错误信息：`Terrain shaders are missing or corrupt. Did you set ROCKY_FILE_PATH to point at the rocky share folder?`
- Rocky引擎在搜索路径中找不到着色器文件
- 搜索路径：`F:/cmo-dev/my_osgearth_web/rocky/share/rocky`

**解决方案:**
1. 创建正确的目录结构：
```bash
mkdir -p "share\rocky\shaders"
```

2. 复制着色器文件到正确位置：
```bash
Copy-Item "redist_desk\shaders\*" "share\rocky\shaders\" -Force
```

3. 验证文件结构：
```
F:/cmo-dev/my_osgearth_web/rocky/
├── share/
│   └── rocky/
│       └── shaders/
│           ├── rocky.terrain.frag
│           ├── rocky.terrain.vert
│           └── ... (其他着色器文件)
```

### 问题3: HTTPS协议不支持 ✅ 已解决

**原因分析:**
- 错误信息：`'https' scheme is not supported`
- Rocky引擎不支持HTTPS协议
- 高程数据源使用了HTTPS URL

**解决方案:**
修改高程数据源为HTTP协议：

```cpp
// 修改前：
elevation->uri = "https://readymap.org/readymap/tiles/1.0.0/116/";

// 修改后：
elevation->uri = "http://readymap.org/readymap/tiles/1.0.0/116/";
```

### 问题4: 批处理文件编码问题 ✅ 已解决

**原因分析:**
- 中文字符在批处理文件中出现乱码
- 导致命令无法正确执行

**解决方案:**
简化批处理文件，移除中文字符：

```batch
@echo off
set ROCKY_FILE_PATH=F:\cmo-dev\my_osgearth_web\rocky\redist_desk
set VSG_FILE_PATH=F:\cmo-dev\my_osgearth_web\rocky\redist_desk

echo Starting Rocky Demo...
echo ROCKY_FILE_PATH=%ROCKY_FILE_PATH%
echo VSG_FILE_PATH=%VSG_FILE_PATH%
echo.

.\rocky_demo.exe --log-level info
pause
```

## 当前状态

### ✅ 已解决的问题
1. **双窗口问题** - 现在只显示一个窗口
2. **着色器缺失** - 地形着色器正确加载
3. **HTTPS协议** - 改用HTTP协议
4. **批处理编码** - 简化为纯ASCII字符

### ⚠️ 剩余问题
1. **高程数据服务** - ReadyMap服务器响应"Unknown"错误
2. **字体警告** - calibri.ttf字体文件缺失（不影响功能）

### 🔧 建议的进一步改进

#### 1. 替换高程数据源
由于ReadyMap服务不稳定，建议使用其他高程数据源：

```cpp
// 选项1: 使用本地高程数据
auto elevation = rocky::TMSElevationLayer::create();
elevation->setName("Local Elevation");
elevation->uri = "file://path/to/local/elevation/tiles";

// 选项2: 使用其他公开服务
auto elevation = rocky::TMSElevationLayer::create();
elevation->setName("Alternative Elevation");
elevation->uri = "http://alternative-elevation-service.com/tiles/{z}/{x}/{y}";
```

#### 2. 添加字体文件
复制calibri.ttf到fonts目录以消除警告：
```bash
Copy-Item "C:\Windows\Fonts\calibri.ttf" "redist_desk\fonts\"
```

#### 3. 网络连接检查
添加网络连接检查以提供更好的错误信息：
```cpp
// 在图层添加前检查网络连接
if (networkAvailable()) {
    layers.add(imagery);
    layers.add(elevation);
} else {
    Log()->warn("Network unavailable, using offline mode");
}
```

## 验证结果

### 成功启动
- ✅ 程序正常启动，只显示一个窗口
- ✅ Vulkan渲染器正确初始化
- ✅ ImGui界面正常显示
- ✅ 着色器正确加载，无着色器错误

### 功能状态
- ✅ **谷歌卫星影像**: 配置正确，应该能正常显示
- ⚠️ **高程数据**: 服务器问题，需要替换数据源
- ✅ **ImGui演示**: 所有演示模块可用
- ✅ **地图操控**: 鼠标交互和缩放功能正常

## 总结

主要的技术问题已经解决，rocky_demo现在可以正常启动并显示ImGui界面。谷歌地球瓦片应该能够正常显示，但高程数据需要替换为可用的服务。程序已经具备了完整的3D地球显示能力，只需要解决数据源的可用性问题。
