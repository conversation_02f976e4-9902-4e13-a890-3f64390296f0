#include <rocky/SRS.h>
#include <rocky/GeoPoint.h>
#include <iostream>
#include <iomanip>
#include <cmath>

using namespace rocky;

bool testWebMercatorTransform() {
    std::cout << "\n=== Web Mercator 坐标转换测试 ===" << std::endl;
    
    SRS wgs84("epsg:4326");
    SRS webMercator("epsg:3857");
    
    if (!wgs84.valid() || !webMercator.valid()) {
        std::cout << "❌ 坐标系创建失败" << std::endl;
        return false;
    }
    
    // 测试关键点：经度-180度应该转换为Web Mercator的-20037508.34278925
    GeoPoint point180W(wgs84, -180.0, 0.0, 0.0);
    GeoPoint mercatorPoint = point180W.transform(webMercator);
    
    std::cout << std::fixed << std::setprecision(6);
    std::cout << "输入: (-180°, 0°)" << std::endl;
    std::cout << "输出: (" << mercatorPoint.x << ", " << mercatorPoint.y << ")" << std::endl;
    std::cout << "期望: (-20037508.342789, 0.000000)" << std::endl;
    
    double expectedX = -20037508.342789;
    double tolerance = 1.0; // 1米误差容忍
    
    if (std::abs(mercatorPoint.x - expectedX) < tolerance && std::abs(mercatorPoint.y) < tolerance) {
        std::cout << "✅ Web Mercator转换测试通过" << std::endl;
        return true;
    } else {
        std::cout << "❌ Web Mercator转换测试失败" << std::endl;
        std::cout << "X误差: " << std::abs(mercatorPoint.x - expectedX) << " 米" << std::endl;
        std::cout << "Y误差: " << std::abs(mercatorPoint.y) << " 米" << std::endl;
        return false;
    }
}

bool testGeodeticToGeocentric() {
    std::cout << "\n=== 大地坐标到地心坐标转换测试 ===" << std::endl;
    
    SRS wgs84("wgs84");
    SRS ecef("geocentric");
    
    if (!wgs84.valid() || !ecef.valid()) {
        std::cout << "❌ 坐标系创建失败" << std::endl;
        return false;
    }
    
    // 测试点：(0°, 0°, 0m) 应该转换为 (6378137, 0, 0)
    GeoPoint origin(wgs84, 0.0, 0.0, 0.0);
    GeoPoint ecefPoint = origin.transform(ecef);
    
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "输入: (0°, 0°, 0m)" << std::endl;
    std::cout << "输出: (" << ecefPoint.x << ", " << ecefPoint.y << ", " << ecefPoint.z << ")" << std::endl;
    std::cout << "期望: (6378137.000, 0.000, 0.000)" << std::endl;
    
    double expectedX = 6378137.0;
    double tolerance = 1.0; // 1米误差容忍
    
    if (std::abs(ecefPoint.x - expectedX) < tolerance && 
        std::abs(ecefPoint.y) < tolerance && 
        std::abs(ecefPoint.z) < tolerance) {
        std::cout << "✅ 地心坐标转换测试通过" << std::endl;
        return true;
    } else {
        std::cout << "❌ 地心坐标转换测试失败" << std::endl;
        return false;
    }
}

bool testRoundTripAccuracy() {
    std::cout << "\n=== 往返转换精度测试 ===" << std::endl;
    
    SRS wgs84("wgs84");
    SRS webMercator("spherical-mercator");
    
    if (!wgs84.valid() || !webMercator.valid()) {
        std::cout << "❌ 坐标系创建失败" << std::endl;
        return false;
    }
    
    // 测试多个关键点的往返转换精度
    struct TestPoint {
        double lon, lat;
        std::string name;
    };
    
    TestPoint testPoints[] = {
        {0.0, 0.0, "赤道原点"},
        {116.4074, 39.9042, "北京"},
        {-74.0060, 40.7128, "纽约"},
        {2.3522, 48.8566, "巴黎"},
        {139.6917, 35.6895, "东京"}
    };
    
    bool allPassed = true;
    double tolerance = 1e-6; // 微度级精度
    
    for (const auto& tp : testPoints) {
        GeoPoint original(wgs84, tp.lon, tp.lat, 0.0);
        GeoPoint mercator = original.transform(webMercator);
        GeoPoint backToWgs = mercator.transform(wgs84);
        
        double lonError = std::abs(backToWgs.x - tp.lon);
        double latError = std::abs(backToWgs.y - tp.lat);
        
        std::cout << tp.name << ": ";
        if (lonError < tolerance && latError < tolerance) {
            std::cout << "✅ 通过 (误差: " << std::scientific << std::setprecision(2) 
                      << std::max(lonError, latError) << "°)" << std::endl;
        } else {
            std::cout << "❌ 失败 (经度误差: " << std::scientific << std::setprecision(2) 
                      << lonError << "°, 纬度误差: " << latError << "°)" << std::endl;
            allPassed = false;
        }
    }
    
    return allPassed;
}

bool testSRSProperties() {
    std::cout << "\n=== SRS属性测试 ===" << std::endl;
    
    SRS wgs84("epsg:4326");
    SRS webMercator("epsg:3857");
    SRS ecef("geocentric");
    
    bool passed = true;
    
    // 测试WGS84属性
    if (!wgs84.isGeodetic() || wgs84.isProjected() || wgs84.isGeocentric()) {
        std::cout << "❌ WGS84属性测试失败" << std::endl;
        passed = false;
    } else {
        std::cout << "✅ WGS84属性正确" << std::endl;
    }
    
    // 测试Web Mercator属性
    if (webMercator.isGeodetic() || !webMercator.isProjected() || webMercator.isGeocentric()) {
        std::cout << "❌ Web Mercator属性测试失败" << std::endl;
        passed = false;
    } else {
        std::cout << "✅ Web Mercator属性正确" << std::endl;
    }
    
    // 测试地心坐标系属性
    if (ecef.isGeodetic() || ecef.isProjected() || !ecef.isGeocentric()) {
        std::cout << "❌ 地心坐标系属性测试失败" << std::endl;
        passed = false;
    } else {
        std::cout << "✅ 地心坐标系属性正确" << std::endl;
    }
    
    return passed;
}

int main() {
    std::cout << "Rocky 坐标转换重构验证程序" << std::endl;
    std::cout << "验证从PROJ库到GeographicLib的重构正确性" << std::endl;
    
    int passedTests = 0;
    int totalTests = 4;
    
    if (testSRSProperties()) passedTests++;
    if (testWebMercatorTransform()) passedTests++;
    if (testGeodeticToGeocentric()) passedTests++;
    if (testRoundTripAccuracy()) passedTests++;
    
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << "测试结果: " << passedTests << "/" << totalTests << " 通过" << std::endl;
    
    if (passedTests == totalTests) {
        std::cout << "🎉 所有测试通过！重构成功！" << std::endl;
        return 0;
    } else {
        std::cout << "⚠️  有测试失败，需要进一步调试" << std::endl;
        return 1;
    }
}
