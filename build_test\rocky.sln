﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{590CA4F0-1C97-3DE5-8703-51EC624CE57B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "apps", "apps", "{9D3E3E0F-7B81-3ABE-AEDB-16354701C0F9}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "rocky", "rocky", "{14AEE036-1D0A-3624-82E9-C12471C8D8F0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{1F37A06B-7132-3EB2-B4F6-6AC9B4EEC1FB}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}"
	ProjectSection(ProjectDependencies) = postProject
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {DA3681F8-7126-3EC7-8914-0DFDAD769971}
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0} = {CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}
		{4A7E47BB-87DF-31D1-8359-E341A943EA02} = {4A7E47BB-87DF-31D1-8359-E341A943EA02}
		{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC} = {A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC}
		{45F6F9CC-165E-3B6A-AB55-B22B054D36C2} = {45F6F9CC-165E-3B6A-AB55-B22B054D36C2}
		{B42E150B-AFDB-371D-A90E-C4F312D69826} = {B42E150B-AFDB-371D-A90E-C4F312D69826}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{859C03B0-CB39-31C3-A6AB-7CF562AD33DF}"
	ProjectSection(ProjectDependencies) = postProject
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6} = {95962E86-63FA-3E2B-8116-A3EE64CDA1E6}
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {DA3681F8-7126-3EC7-8914-0DFDAD769971}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{DA3681F8-7126-3EC7-8914-0DFDAD769971}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky", "src\rocky\rocky.vcxproj", "{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}"
	ProjectSection(ProjectDependencies) = postProject
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {DA3681F8-7126-3EC7-8914-0DFDAD769971}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky_demo", "src\apps\rocky_demo\rocky_demo.vcxproj", "{4A7E47BB-87DF-31D1-8359-E341A943EA02}"
	ProjectSection(ProjectDependencies) = postProject
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {DA3681F8-7126-3EC7-8914-0DFDAD769971}
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0} = {CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky_engine", "src\apps\rocky_engine\rocky_engine.vcxproj", "{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC}"
	ProjectSection(ProjectDependencies) = postProject
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {DA3681F8-7126-3EC7-8914-0DFDAD769971}
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0} = {CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky_simple", "src\apps\rocky_simple\rocky_simple.vcxproj", "{45F6F9CC-165E-3B6A-AB55-B22B054D36C2}"
	ProjectSection(ProjectDependencies) = postProject
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {DA3681F8-7126-3EC7-8914-0DFDAD769971}
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0} = {CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky_tests", "src\tests\rocky_tests.vcxproj", "{B42E150B-AFDB-371D-A90E-C4F312D69826}"
	ProjectSection(ProjectDependencies) = postProject
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {DA3681F8-7126-3EC7-8914-0DFDAD769971}
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0} = {CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.Debug|x64.ActiveCfg = Debug|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.Debug|x64.Build.0 = Debug|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.Release|x64.ActiveCfg = Release|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.Release|x64.Build.0 = Release|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{859C03B0-CB39-31C3-A6AB-7CF562AD33DF}.Debug|x64.ActiveCfg = Debug|x64
		{859C03B0-CB39-31C3-A6AB-7CF562AD33DF}.Release|x64.ActiveCfg = Release|x64
		{859C03B0-CB39-31C3-A6AB-7CF562AD33DF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{859C03B0-CB39-31C3-A6AB-7CF562AD33DF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.Debug|x64.ActiveCfg = Debug|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.Debug|x64.Build.0 = Debug|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.Release|x64.ActiveCfg = Release|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.Release|x64.Build.0 = Release|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.Debug|x64.ActiveCfg = Debug|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.Debug|x64.Build.0 = Debug|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.Release|x64.ActiveCfg = Release|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.Release|x64.Build.0 = Release|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4A7E47BB-87DF-31D1-8359-E341A943EA02}.Debug|x64.ActiveCfg = Debug|x64
		{4A7E47BB-87DF-31D1-8359-E341A943EA02}.Debug|x64.Build.0 = Debug|x64
		{4A7E47BB-87DF-31D1-8359-E341A943EA02}.Release|x64.ActiveCfg = Release|x64
		{4A7E47BB-87DF-31D1-8359-E341A943EA02}.Release|x64.Build.0 = Release|x64
		{4A7E47BB-87DF-31D1-8359-E341A943EA02}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4A7E47BB-87DF-31D1-8359-E341A943EA02}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4A7E47BB-87DF-31D1-8359-E341A943EA02}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4A7E47BB-87DF-31D1-8359-E341A943EA02}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC}.Debug|x64.ActiveCfg = Debug|x64
		{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC}.Debug|x64.Build.0 = Debug|x64
		{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC}.Release|x64.ActiveCfg = Release|x64
		{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC}.Release|x64.Build.0 = Release|x64
		{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{45F6F9CC-165E-3B6A-AB55-B22B054D36C2}.Debug|x64.ActiveCfg = Debug|x64
		{45F6F9CC-165E-3B6A-AB55-B22B054D36C2}.Debug|x64.Build.0 = Debug|x64
		{45F6F9CC-165E-3B6A-AB55-B22B054D36C2}.Release|x64.ActiveCfg = Release|x64
		{45F6F9CC-165E-3B6A-AB55-B22B054D36C2}.Release|x64.Build.0 = Release|x64
		{45F6F9CC-165E-3B6A-AB55-B22B054D36C2}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{45F6F9CC-165E-3B6A-AB55-B22B054D36C2}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{45F6F9CC-165E-3B6A-AB55-B22B054D36C2}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{45F6F9CC-165E-3B6A-AB55-B22B054D36C2}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{B42E150B-AFDB-371D-A90E-C4F312D69826}.Debug|x64.ActiveCfg = Debug|x64
		{B42E150B-AFDB-371D-A90E-C4F312D69826}.Debug|x64.Build.0 = Debug|x64
		{B42E150B-AFDB-371D-A90E-C4F312D69826}.Release|x64.ActiveCfg = Release|x64
		{B42E150B-AFDB-371D-A90E-C4F312D69826}.Release|x64.Build.0 = Release|x64
		{B42E150B-AFDB-371D-A90E-C4F312D69826}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{B42E150B-AFDB-371D-A90E-C4F312D69826}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{B42E150B-AFDB-371D-A90E-C4F312D69826}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{B42E150B-AFDB-371D-A90E-C4F312D69826}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6} = {590CA4F0-1C97-3DE5-8703-51EC624CE57B}
		{859C03B0-CB39-31C3-A6AB-7CF562AD33DF} = {590CA4F0-1C97-3DE5-8703-51EC624CE57B}
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {590CA4F0-1C97-3DE5-8703-51EC624CE57B}
		{4A7E47BB-87DF-31D1-8359-E341A943EA02} = {9D3E3E0F-7B81-3ABE-AEDB-16354701C0F9}
		{A8AA70E5-C4D6-3EB8-AA30-E4EB87A617AC} = {9D3E3E0F-7B81-3ABE-AEDB-16354701C0F9}
		{45F6F9CC-165E-3B6A-AB55-B22B054D36C2} = {9D3E3E0F-7B81-3ABE-AEDB-16354701C0F9}
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0} = {14AEE036-1D0A-3624-82E9-C12471C8D8F0}
		{B42E150B-AFDB-371D-A90E-C4F312D69826} = {1F37A06B-7132-3EB2-B4F6-6AC9B4EEC1FB}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {87E69A90-261F-327D-9863-3FF2C4E26FD7}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
