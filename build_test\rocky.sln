﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{590CA4F0-1C97-3DE5-8703-51EC624CE57B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "rocky", "rocky", "{14AEE036-1D0A-3624-82E9-C12471C8D8F0}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}"
	ProjectSection(ProjectDependencies) = postProject
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {DA3681F8-7126-3EC7-8914-0DFDAD769971}
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0} = {CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{859C03B0-CB39-31C3-A6AB-7CF562AD33DF}"
	ProjectSection(ProjectDependencies) = postProject
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6} = {95962E86-63FA-3E2B-8116-A3EE64CDA1E6}
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {DA3681F8-7126-3EC7-8914-0DFDAD769971}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{DA3681F8-7126-3EC7-8914-0DFDAD769971}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky", "src\rocky\rocky.vcxproj", "{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}"
	ProjectSection(ProjectDependencies) = postProject
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {DA3681F8-7126-3EC7-8914-0DFDAD769971}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.Debug|x64.ActiveCfg = Debug|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.Debug|x64.Build.0 = Debug|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.Release|x64.ActiveCfg = Release|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.Release|x64.Build.0 = Release|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{859C03B0-CB39-31C3-A6AB-7CF562AD33DF}.Debug|x64.ActiveCfg = Debug|x64
		{859C03B0-CB39-31C3-A6AB-7CF562AD33DF}.Release|x64.ActiveCfg = Release|x64
		{859C03B0-CB39-31C3-A6AB-7CF562AD33DF}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{859C03B0-CB39-31C3-A6AB-7CF562AD33DF}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.Debug|x64.ActiveCfg = Debug|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.Debug|x64.Build.0 = Debug|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.Release|x64.ActiveCfg = Release|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.Release|x64.Build.0 = Release|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DA3681F8-7126-3EC7-8914-0DFDAD769971}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.Debug|x64.ActiveCfg = Debug|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.Debug|x64.Build.0 = Debug|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.Release|x64.ActiveCfg = Release|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.Release|x64.Build.0 = Release|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{95962E86-63FA-3E2B-8116-A3EE64CDA1E6} = {590CA4F0-1C97-3DE5-8703-51EC624CE57B}
		{859C03B0-CB39-31C3-A6AB-7CF562AD33DF} = {590CA4F0-1C97-3DE5-8703-51EC624CE57B}
		{DA3681F8-7126-3EC7-8914-0DFDAD769971} = {590CA4F0-1C97-3DE5-8703-51EC624CE57B}
		{CF93A95C-39AF-369D-B363-1AFFFCDCE8C0} = {14AEE036-1D0A-3624-82E9-C12471C8D8F0}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {87E69A90-261F-327D-9863-3FF2C4E26FD7}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
