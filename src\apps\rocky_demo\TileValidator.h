/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#pragma once

#include <rocky/rocky.h>
#include <filesystem>
#include <fstream>
#include <sstream>
#include <iomanip>

namespace TileValidation
{
    /**
     * 瓦片验证器 - 用于验证高程和影像瓦片的对应关系和数据正确性
     */
    class TileValidator
    {
    public:
        struct TileInfo
        {
            int z, x, y;
            std::string url;
            std::string filename;
            size_t dataSize;
            std::chrono::system_clock::time_point downloadTime;
            
            std::string getCoordinateString() const {
                return "z" + std::to_string(z) + "_x" + std::to_string(x) + "_y" + std::to_string(y);
            }
        };

        TileValidator(const std::string& cacheDir) : cacheDir_(cacheDir) 
        {
            std::filesystem::create_directories(cacheDir_);
            std::filesystem::create_directories(cacheDir_ + "/imagery");
            std::filesystem::create_directories(cacheDir_ + "/elevation");
            std::filesystem::create_directories(cacheDir_ + "/analysis");
        }

        /**
         * 记录影像瓦片下载
         */
        void recordImageryTile(int z, int x, int y, const std::string& url, const std::vector<uint8_t>& data)
        {
            TileInfo info;
            info.z = z; info.x = x; info.y = y;
            info.url = url;
            info.dataSize = data.size();
            info.downloadTime = std::chrono::system_clock::now();
            info.filename = "imagery_" + info.getCoordinateString() + ".jpg";
            
            // 保存瓦片文件
            std::string filepath = cacheDir_ + "/imagery/" + info.filename;
            std::ofstream file(filepath, std::ios::binary);
            file.write(reinterpret_cast<const char*>(data.data()), data.size());
            file.close();
            
            imageryTiles_[info.getCoordinateString()] = info;
            
            rocky::Log()->info("[TileValidator] Imagery tile saved: {} ({} bytes)", info.filename, info.dataSize);
        }

        /**
         * 记录高程瓦片下载
         */
        void recordElevationTile(int z, int x, int y, const std::string& url, const std::vector<uint8_t>& data)
        {
            TileInfo info;
            info.z = z; info.x = x; info.y = y;
            info.url = url;
            info.dataSize = data.size();
            info.downloadTime = std::chrono::system_clock::now();
            info.filename = "elevation_" + info.getCoordinateString() + ".tif";
            
            // 保存瓦片文件
            std::string filepath = cacheDir_ + "/elevation/" + info.filename;
            std::ofstream file(filepath, std::ios::binary);
            file.write(reinterpret_cast<const char*>(data.data()), data.size());
            file.close();
            
            elevationTiles_[info.getCoordinateString()] = info;
            
            rocky::Log()->info("[TileValidator] Elevation tile saved: {} ({} bytes)", info.filename, info.dataSize);
            
            // 分析高程数据
            analyzeElevationData(info, data);
        }

        /**
         * 检查瓦片对应关系
         */
        void validateTileCorrespondence()
        {
            std::ofstream report(cacheDir_ + "/analysis/tile_correspondence_report.txt");
            report << "=== 瓦片对应关系验证报告 ===\n\n";
            
            report << "影像瓦片总数: " << imageryTiles_.size() << "\n";
            report << "高程瓦片总数: " << elevationTiles_.size() << "\n\n";
            
            // 检查每个影像瓦片是否有对应的高程瓦片
            int matchedTiles = 0;
            int missingElevation = 0;
            
            for (const auto& [coord, imageryInfo] : imageryTiles_)
            {
                auto elevationIt = elevationTiles_.find(coord);
                if (elevationIt != elevationTiles_.end())
                {
                    matchedTiles++;
                    report << "✅ 匹配: " << coord << "\n";
                    report << "   影像: " << imageryInfo.filename << " (" << imageryInfo.dataSize << " bytes)\n";
                    report << "   高程: " << elevationIt->second.filename << " (" << elevationIt->second.dataSize << " bytes)\n\n";
                }
                else
                {
                    missingElevation++;
                    report << "❌ 缺失高程: " << coord << "\n";
                    report << "   影像: " << imageryInfo.filename << " (" << imageryInfo.dataSize << " bytes)\n\n";
                }
            }
            
            // 检查孤立的高程瓦片
            int orphanedElevation = 0;
            for (const auto& [coord, elevationInfo] : elevationTiles_)
            {
                if (imageryTiles_.find(coord) == imageryTiles_.end())
                {
                    orphanedElevation++;
                    report << "⚠️ 孤立高程: " << coord << "\n";
                    report << "   高程: " << elevationInfo.filename << " (" << elevationInfo.dataSize << " bytes)\n\n";
                }
            }
            
            report << "=== 统计摘要 ===\n";
            report << "匹配的瓦片对: " << matchedTiles << "\n";
            report << "缺失高程瓦片: " << missingElevation << "\n";
            report << "孤立高程瓦片: " << orphanedElevation << "\n";
            
            double matchRate = imageryTiles_.empty() ? 0.0 : (double)matchedTiles / imageryTiles_.size() * 100.0;
            report << "匹配率: " << std::fixed << std::setprecision(1) << matchRate << "%\n";
            
            report.close();
            
            rocky::Log()->info("[TileValidator] Correspondence report saved to: {}/analysis/tile_correspondence_report.txt", cacheDir_);
            rocky::Log()->info("[TileValidator] Tile correspondence: {}/{} matched ({:.1f}%)", 
                              matchedTiles, imageryTiles_.size(), matchRate);
        }

        /**
         * 分析高程数据的正确性
         */
        void analyzeElevationData(const TileInfo& info, const std::vector<uint8_t>& data)
        {
            // 这里我们需要解析TIFF数据来验证高程值
            // 由于我们已经有LibTIFF集成，可以使用它来分析数据
            
            std::string analysisFile = cacheDir_ + "/analysis/elevation_analysis_" + info.getCoordinateString() + ".txt";
            std::ofstream analysis(analysisFile);
            
            analysis << "=== 高程瓦片分析 ===\n";
            analysis << "坐标: z=" << info.z << ", x=" << info.x << ", y=" << info.y << "\n";
            analysis << "URL: " << info.url << "\n";
            analysis << "文件大小: " << info.dataSize << " bytes\n";
            analysis << "下载时间: " << std::chrono::duration_cast<std::chrono::seconds>(
                info.downloadTime.time_since_epoch()).count() << "\n\n";
            
            // TIFF文件头分析
            if (data.size() >= 8)
            {
                // 检查TIFF魔数
                bool isLittleEndian = (data[0] == 0x49 && data[1] == 0x49);
                bool isBigEndian = (data[0] == 0x4D && data[1] == 0x4D);
                
                analysis << "TIFF格式检查:\n";
                if (isLittleEndian)
                {
                    analysis << "✅ 有效的TIFF文件 (Little Endian)\n";
                    uint16_t magic = data[2] | (data[3] << 8);
                    analysis << "   魔数: 0x" << std::hex << magic << std::dec << "\n";
                }
                else if (isBigEndian)
                {
                    analysis << "✅ 有效的TIFF文件 (Big Endian)\n";
                    uint16_t magic = (data[2] << 8) | data[3];
                    analysis << "   魔数: 0x" << std::hex << magic << std::dec << "\n";
                }
                else
                {
                    analysis << "❌ 无效的TIFF文件格式\n";
                    analysis << "   前4字节: " << std::hex;
                    for (int i = 0; i < 4 && i < data.size(); ++i)
                        analysis << "0x" << (int)data[i] << " ";
                    analysis << std::dec << "\n";
                }
            }
            
            // 地理坐标计算
            analysis << "\n地理坐标计算 (Web Mercator):\n";
            double n = std::pow(2.0, info.z);
            double lon_deg = info.x / n * 360.0 - 180.0;
            double lat_rad = std::atan(std::sinh(M_PI * (1 - 2 * info.y / n)));
            double lat_deg = lat_rad * 180.0 / M_PI;
            
            analysis << "   西北角: (" << std::fixed << std::setprecision(6) << lat_deg << ", " << lon_deg << ")\n";
            
            lon_deg = (info.x + 1) / n * 360.0 - 180.0;
            lat_rad = std::atan(std::sinh(M_PI * (1 - 2 * (info.y + 1) / n)));
            lat_deg = lat_rad * 180.0 / M_PI;
            
            analysis << "   东南角: (" << lat_deg << ", " << lon_deg << ")\n";
            
            analysis.close();
        }

        /**
         * 生成完整的验证报告
         */
        void generateFullReport()
        {
            validateTileCorrespondence();
            
            std::ofstream summary(cacheDir_ + "/analysis/validation_summary.txt");
            summary << "=== Rocky 瓦片验证完整报告 ===\n\n";
            summary << "生成时间: " << std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()).count() << "\n\n";
            
            summary << "数据源:\n";
            summary << "- 影像: Google Maps (HTTPS)\n";
            summary << "- 高程: ReadyMap.org (HTTPS)\n";
            summary << "- 坐标系: Web Mercator (EPSG:3857)\n\n";
            
            summary << "缓存统计:\n";
            summary << "- 影像瓦片: " << imageryTiles_.size() << " 个\n";
            summary << "- 高程瓦片: " << elevationTiles_.size() << " 个\n";
            
            size_t totalImagerySize = 0, totalElevationSize = 0;
            for (const auto& [coord, info] : imageryTiles_)
                totalImagerySize += info.dataSize;
            for (const auto& [coord, info] : elevationTiles_)
                totalElevationSize += info.dataSize;
                
            summary << "- 影像数据总大小: " << totalImagerySize / 1024 << " KB\n";
            summary << "- 高程数据总大小: " << totalElevationSize / 1024 << " KB\n";
            
            summary.close();
            
            rocky::Log()->info("[TileValidator] Full validation report generated in: {}/analysis/", cacheDir_);
        }

    private:
        std::string cacheDir_;
        std::map<std::string, TileInfo> imageryTiles_;
        std::map<std::string, TileInfo> elevationTiles_;
    };

    // 全局验证器实例
    inline std::unique_ptr<TileValidator> g_validator;
    
    // 初始化验证器
    inline void initializeValidator(const std::string& cacheDir)
    {
        g_validator = std::make_unique<TileValidator>(cacheDir);
    }
    
    // 获取验证器实例
    inline TileValidator* getValidator()
    {
        return g_validator.get();
    }
}
