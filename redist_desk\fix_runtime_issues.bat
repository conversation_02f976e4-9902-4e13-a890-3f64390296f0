@echo off
echo 正在修复Rocky运行时问题...
echo.

:: 1. 复制着色器文件
echo 步骤1: 复制着色器文件...
if exist "F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders" (
    xcopy "F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders" "shaders\" /S /Y
    echo 着色器文件已复制到 shaders\ 目录
) else (
    echo 警告: 找不到着色器源文件
)

:: 2. 复制字体文件
echo 步骤2: 复制字体文件...
if exist "F:\cmo-dev\my_osgearth_web\rocky\data\fonts" (
    xcopy "F:\cmo-dev\my_osgearth_web\rocky\data\fonts" "fonts\" /S /Y
    echo 字体文件已复制到 fonts\ 目录
) else (
    echo 警告: 找不到字体文件
)

:: 3. 设置环境变量
echo 步骤3: 设置环境变量...
set ROCKY_FILE_PATH=%~dp0
set VSG_FILE_PATH=%~dp0
echo ROCKY_FILE_PATH=%ROCKY_FILE_PATH%
echo VSG_FILE_PATH=%VSG_FILE_PATH%

:: 4. 创建启动脚本
echo 步骤4: 创建启动脚本...
(
echo @echo off
echo :: 设置着色器和资源路径
echo set ROCKY_FILE_PATH=%~dp0
echo set VSG_FILE_PATH=%~dp0
echo.
echo :: 启动rocky_simple
echo echo 正在启动Rocky + Google Maps...
echo echo 环境变量:
echo echo   ROCKY_FILE_PATH=%%ROCKY_FILE_PATH%%
echo echo   VSG_FILE_PATH=%%VSG_FILE_PATH%%
echo echo.
echo .\rocky_simple.exe
echo pause
) > run_rocky_simple.bat

echo.
echo 修复完成！
echo 使用 run_rocky_simple.bat 启动程序，或者：
echo 1. 手动设置环境变量 ROCKY_FILE_PATH 为当前目录
echo 2. 确保代理 127.0.0.1:10809 正在运行
echo 3. 运行 rocky_simple.exe
echo.
pause 