# Rocky库使用说明

## 概述
Rocky是一个现代化的C++地理信息处理库，基于VSG/Vulkan图形引擎，支持高性能地理数据处理和地图渲染。

## 编译环境
- **编译器**: Visual Studio 2022
- **C++标准**: C++17
- **字符编码**: UTF-8
- **目标平台**: Windows x64

## 库文件说明

### 主要文件
- `rocky.dll` - 核心动态链接库 (2.4MB)
- `rocky.lib` - 静态链接库，用于链接时使用
- `rocky_simple.exe` - 简单示例应用程序
- `rocky_engine.exe` - 引擎示例应用程序

### 依赖库
项目依赖以下第三方库：
- **VSG** - 现代Vulkan图形引擎
- **GDAL** - 地理数据访问库，支持多种地理数据格式
- **PROJ** - 地理投影转换库
- **nlohmann_json** - JSON解析库
- **spdlog** - 高性能日志库
- **cpp-httplib** - HTTP客户端库

## 功能特性

### 已启用功能
- ✅ **VSG/Vulkan渲染** - 现代化图形渲染引擎
- ✅ **GDAL支持** - 多种地理数据格式支持
- ✅ **HTTP支持** - 基于cpp-httplib的轻量级HTTP客户端
- ✅ **地理投影** - 完整的坐标系转换支持

### 已禁用功能（轻量化配置）
- ❌ **HTTPS支持** - 已禁用OpenSSL依赖
- ❌ **MBTiles支持** - 已禁用SQLite3和zlib依赖
- ❌ **Qt支持** - 已禁用Qt界面库
- ❌ **CURL支持** - 使用httplib替代

## 使用方法

### 基本集成
1. 将`rocky.dll`复制到您的应用程序目录
2. 在项目中链接`rocky.lib`
3. 包含必要的头文件

### 示例代码
```cpp
#include <rocky/rocky.h>

int main() {
    // 初始化Rocky库
    rocky::Map map;
    
    // 添加图像图层
    auto imageLayer = rocky::GDALImageLayer::create();
    imageLayer->setURL("path/to/your/image.tif");
    map.addLayer(imageLayer);
    
    // 创建VSG应用程序
    auto app = rocky::Application::create();
    app->setMap(map);
    
    return app->run();
}
```

### CMake集成
```cmake
find_package(rocky REQUIRED)
target_link_libraries(your_app rocky::rocky)
```

## 示例应用程序

### rocky_simple.exe
最简单的Rocky应用程序，演示基本的地图显示功能。

### rocky_engine.exe
更复杂的示例，展示Rocky引擎的高级功能。

## 数据格式支持

### 图像格式
- GeoTIFF (.tif, .tiff)
- PNG (.png)
- JPEG (.jpg, .jpeg)
- WebP (.webp)

### 矢量格式
- Shapefile (.shp)
- GeoJSON (.json, .geojson)
- KML (.kml)

### 网络服务
- WMS (Web Map Service)
- WMTS (Web Map Tile Service)
- TMS (Tile Map Service)

## 性能特点
- 基于现代Vulkan API的高性能渲染
- 多线程数据处理
- 内存映射文件访问
- 高效的几何数据处理

## 注意事项

1. **依赖库版本**: 确保所有依赖库版本兼容
2. **Vulkan驱动**: 需要支持Vulkan 1.1以上的显卡驱动
3. **字符编码**: 所有字符串应使用UTF-8编码
4. **线程安全**: 某些API可能不是线程安全的，需要注意同步

## 故障排除

### 常见问题
1. **找不到DLL**: 确保所有依赖库都在PATH中或应用程序目录中
2. **Vulkan初始化失败**: 检查显卡驱动是否支持Vulkan
3. **数据加载失败**: 检查文件路径和权限

### 调试建议
- 使用spdlog的日志输出来诊断问题
- 检查Rocky的错误状态码
- 验证输入数据的有效性

## 许可证
Rocky库遵循开源许可证，具体信息请参考LICENSE文件。

## 更多信息
- **官方网站**: https://github.com/pelicanmapping/rocky
- **文档**: 请参考源代码中的示例和头文件注释
- **社区支持**: GitHub Issues

---
*编译时间: 2024年*
*编译器: Visual Studio 2022*
*目标平台: Windows x64* 