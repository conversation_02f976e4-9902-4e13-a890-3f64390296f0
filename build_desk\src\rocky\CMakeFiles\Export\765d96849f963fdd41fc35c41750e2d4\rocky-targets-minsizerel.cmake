#----------------------------------------------------------------
# Generated CMake target import file for configuration "MinSizeRel".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "rocky::rocky" for configuration "MinSizeRel"
set_property(TARGET rocky::rocky APPEND PROPERTY IMPORTED_CONFIGURATIONS MINSIZEREL)
set_target_properties(rocky::rocky PROPERTIES
  IMPORTED_IMPLIB_MINSIZEREL "${_IMPORT_PREFIX}/lib/rocky.lib"
  IMPORTED_LINK_DEPENDENT_LIBRARIES_MINSIZEREL "GeographicLib::GeographicLib_SHARED;GEOS::geos;vsgXchange::vsgXchange"
  IMPORTED_LOCATION_MINSIZEREL "${_IMPORT_PREFIX}/bin/rocky.dll"
  )

list(APPEND _cmake_import_check_targets rocky::rocky )
list(APPEND _cmake_import_check_files_for_rocky::rocky "${_IMPORT_PREFIX}/lib/rocky.lib" "${_IMPORT_PREFIX}/bin/rocky.dll" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
