prefix=${pcfiledir}/../..
CONFIG_VERSION=3.11.0
CONFIG_INST_PREFIX=${prefix}
CONFIG_INST_LIBS=-LIBPATH:${prefix}/lib gdal
CONFIG_INST_CFLAGS="-I${prefix}/../include"
CONFIG_INST_DATA=${prefix}/../share/gdal
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${exec_prefix}/include
datadir=${CONFIG_INST_DATA}

Name: libgdal
Description: Geospatial Data Abstraction Library
Version: ${CONFIG_VERSION}
Libs: ${CONFIG_INST_LIBS}
Libs.private: wbemuuid ${prefix}/lib/qhull_rd.lib ${prefix}/lib/geotiff_d_i.lib ${prefix}/lib/tiffd.lib ws2_32 ${prefix}/lib/Lerc.lib ${prefix}/lib/jpeg.lib ${prefix}/lib/libpng16d.lib ${prefix}/lib/libwebp.lib ${prefix}/lib/libsharpyuv.lib shlwapi ole32 windowscodecs ${prefix}/lib/zlibd.lib ${prefix}/lib/sqlite3.lib ${prefix}/lib/libexpatd.lib ${prefix}/lib/geos_c.lib ${prefix}/lib/libcurl-d.lib psapi ${prefix}/lib/proj_d.lib ${prefix}/lib/json-c.lib
Cflags: ${CONFIG_INST_CFLAGS}

