@echo off
title Rocky库一键编译脚本
color 0A
echo.
echo ========================================
echo   Rocky库一键编译脚本
echo   Visual Studio 2022 + C++17
echo   编译目录: F:/rockyb2
echo ========================================
echo.

echo 第1步: 清理编译目录...
if exist "F:/rockyb2" (
    echo 正在删除旧的编译目录...
    rmdir /s /q "F:/rockyb2"
    if %errorlevel% neq 0 (
        echo 清理失败！请手动删除 F:/rockyb2 目录
        pause
        exit /b 1
    )
    echo 清理完成！
) else (
    echo 编译目录不存在，无需清理
)

echo.
echo 第2步: 配置CMake项目（无GDAL支持）...
call build_config_core.bat
if %errorlevel% neq 0 (
    echo 配置失败！
    pause
    exit /b 1
)

echo.
echo 第3步: 编译项目...
cd /d "F:/rockyb2"
echo 开始编译 Release 版本...
cmake --build . --config Release -j

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 第4步: 创建发布包...
if not exist "redist_desk" mkdir "redist_desk"

echo 复制文件到发布目录...
xcopy "src\rocky\Release\*.dll" "redist_desk\" /Y /Q
xcopy "src\rocky\Release\*.lib" "redist_desk\" /Y /Q
xcopy "src\apps\rocky_simple\Release\*.exe" "redist_desk\" /Y /Q
xcopy "src\apps\rocky_engine\Release\*.exe" "redist_desk\" /Y /Q

echo.
echo ========================================
echo   编译完成！
echo ========================================
echo.
echo 生成文件位置: F:/rockyb2/redist_desk/
echo.
echo 主要文件:
echo   - rocky.dll        (核心库)
echo   - rocky.lib        (静态库)
echo   - rocky_simple.exe (简单示例)
echo   - rocky_engine.exe (引擎示例)
echo.
echo 依赖库: 已自动复制所有必需的DLL文件
echo.
echo 使用说明请参考: Rocky库使用说明.md
echo.
echo ========================================
echo.
pause 