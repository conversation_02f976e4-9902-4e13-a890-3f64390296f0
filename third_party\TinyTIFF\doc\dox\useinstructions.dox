/*!

\page page_useinstructions Usage/Linking Instructions

This page explains how to link against TinyTIFF.
.

\tableofcontents


\section page_useinstructions_CMAKE Use TinyTIFF with CMake
When using CMake to build your application, you can simply use \c target_link_libraries() to link against TinyTIFF and use CMake's \c find_package() to enable that (see https://github.com/jkriege2/TinyTIFF/tree/master/tests/extcmake_tinytiff_test for an example):

\code{.cmake}
  cmake_minimum_required(VERSION 3.10)

  project(extcmake_tinytiff_test CXX)

  # find TinyTIFF library
  find_package(TinyTIFF)

  # add an executable
  add_executable(${PROJECT_NAME}
    extcmake_tinytiff_test.cpp
  )

  # link against TinyTIFF
  target_link_libraries(${PROJECT_NAME} TinyTIFF::TinyTIFF)
\endcode

\note Note that you may have to provide CMake with search pathes to find the library, e.g. set \c TinyTIFF_DIR to \c <INSTALLDIR>/lib/cmake/TinyTIFF so <PERSON><PERSON><PERSON> finds the \c TinyTIFFConfig.cmake file there.



\section page_useinstructions_CMAKE_FETCHCONTENT Use TinyTIFF with CMake & FetchCotent-API
When using CMake to build your application, you can also use CMake's FetchContent-API to download and include TinyTIFF (see https://github.com/jkriege2/TinyTIFF/tree/master/tests/extcmake_fetchcontent_tinytiff_test for an example and https://cmake.org/cmake/help/latest/module/FetchContent.html for documentation on FetchContent):

\code{.cmake}
  # set minimum required CMake-Version
  cmake_minimum_required(VERSION 3.23)

  # set Project name
  project(extcmake_fetchcontent_tinytiff_test LANGUAGES CXX)

  # include TinyTIFF via FetchContent-API:
  # ... first load the FetchContent-API:
  include(FetchContent) # once in the project to include the module
  # ... now declare TinyTIFF
  FetchContent_Declare(TinyTIFF
                       GIT_REPOSITORY https://github.com/jkriege2/TinyTIFF.git
                       # GIT_TAG        3.1.0.0
                       )
                       
  # ... finally make TinyTIFF available
  FetchContent_MakeAvailable(TinyTIFF)

  add_executable(${PROJECT_NAME}
    extcmake_fetchcontent_tinytiff_test.cpp
  )

  target_link_libraries(${PROJECT_NAME} TinyTIFF::TinyTIFF)
\endcode


\section page_useinstructions_NOCMAKE Use TinyTIFF without CMake
You can also link against TinyTIFF without using CMake. For this you simply have to supply the library as a parameter to your compile/link run, e.g. for GCC:

\code{.sh}
    $ g++ main.cpp -o out -I<INSTALLDIR>/include -L<INSTALLDIR>/lib -lTinyTIFF_Release
\endcode

The \c -I -option provides the search directory for include-files (i.e. headers) and the \c -L -option the search path for the link libraries. Here we link against the release version \c TinyTIFF_Release, i.e. with config-decorated filename (see build options!). Check for the actual name of the libs on your system and plug in the appropriate name! If you build the library as a shared lib, you have to link e.g. against \c TinyTIFFShared_Release, as the build-scripts add the word \c hared to the library name for shared libs to distinguish them from the static libs.

Note that you might also have to provide additional libraries, depending on your system. 



*/