cmake_minimum_required(VERSION 3.20)
project(final_validation)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

if(MSVC)
    add_compile_options(/utf-8)
endif()

set(ROCKY_INCLUDE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/include")
set(ROCKY_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/lib")
set(ROCKY_BIN_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/bin")

add_executable(final_validation final_validation.cpp)
target_include_directories(final_validation PRIVATE ${ROCKY_INCLUDE_DIR})
target_link_libraries(final_validation PRIVATE "${ROCKY_LIB_DIR}/rocky.lib")

if(WIN32)
    add_custom_command(TARGET final_validation POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${ROCKY_BIN_DIR}/rocky.dll"
        $<TARGET_FILE_DIR:final_validation>)
        
    add_custom_command(TARGET final_validation POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "C:/dev/vcpkg/installed/x64-windows/bin/GeographicLib.dll"
        $<TARGET_FILE_DIR:final_validation>)
endif()
