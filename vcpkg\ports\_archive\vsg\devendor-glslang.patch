diff --git a/CMakeLists.txt b/CMakeLists.txt
index a33dda40..40ff7e13 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -37,12 +37,14 @@ find_package(Vulkan ${Vulkan_MIN_VERSION} REQUIRED)
 
 find_package(Threads REQUIRED)
 
+find_package(glslang CONFIG REQUIRED)
+
 # Set the instrumentation level to compile into sources
 set(VSG_MAX_INSTRUMENTATION_LEVEL 1 CACHE STRING "Set the instrumentation level to build into the VSG ibrary, 0 for off, 1 coarse grained, 2 medium, 3 fine grained." )
 
 # Enable/disable shader compilation support that pulls in glslang
 set(VSG_SUPPORTS_ShaderCompiler  1 CACHE STRING "Optional shader compiler support, 0 for off, 1 for enabled." )
-if (VSG_SUPPORTS_ShaderCompiler)
+if (FALSE) # VSG_SUPPORTS_ShaderCompiler)
     if (NOT EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/src/glslang/build_vars.cmake)
 
         if (Git_FOUND)
diff --git a/src/vsg/CMakeLists.txt b/src/vsg/CMakeLists.txt
index 7b315585..ef15b333 100644
--- a/src/vsg/CMakeLists.txt
+++ b/src/vsg/CMakeLists.txt
@@ -247,13 +247,22 @@ set(SOURCES
 if (${VSG_SUPPORTS_ShaderCompiler})
 
     # include glslang source code directly into the VulkanScenegraph library build.
-    include(../glslang/build_vars.cmake)
+    # include(../glslang/build_vars.cmake)
 endif()
 
 # set up library dependencies
 set(LIBRARIES PUBLIC
         Vulkan::Vulkan
         Threads::Threads
+    PRIVATE
+         glslang::glslang
+         glslang::OSDependent
+         glslang::MachineIndependent
+         glslang::GenericCodeGen
+         glslang::glslang-default-resource-limits
+         glslang::OGLCompiler
+         glslang::SPVRemapper
+         glslang::SPIRV
 )
 
 # Check for std::atomic
@@ -392,9 +401,6 @@ target_include_directories(vsg
     PUBLIC
         $<BUILD_INTERFACE:${VSG_SOURCE_DIR}/include>
         $<BUILD_INTERFACE:${VSG_BINARY_DIR}/include>
-    PRIVATE
-        $<BUILD_INTERFACE:${VSG_SOURCE_DIR}/src/glslang>
-        $<BUILD_INTERFACE:${GLSLANG_GENERATED_INCLUDEDIR}>
 )
 
 target_link_libraries(vsg ${LIBRARIES})
diff --git a/src/vsg/utils/ShaderCompiler.cpp b/src/vsg/utils/ShaderCompiler.cpp
index a8f8dec9..2ca86f87 100644
--- a/src/vsg/utils/ShaderCompiler.cpp
+++ b/src/vsg/utils/ShaderCompiler.cpp
@@ -20,7 +20,7 @@ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLI
 #include <vsg/utils/ShaderCompiler.h>
 
 #if VSG_SUPPORTS_ShaderCompiler
-#    include <SPIRV/GlslangToSpv.h>
+#    include <glslang/SPIRV/GlslangToSpv.h>
 #    include <glslang/Public/ResourceLimits.h>
 #    include <glslang/Public/ShaderLang.h>
 #endif
