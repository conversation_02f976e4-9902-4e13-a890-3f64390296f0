/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#pragma once
#include <rocky/GeosAdapter.h>
#include <rocky/vsg/ecs/LineView.h>
#include <rocky/vsg/ecs/MeshView.h>
#include "helpers.h"

using namespace ROCKY_NAMESPACE;

auto Demo_GeosGeometry = [](Application& app)
{
#ifdef ROCKY_HAS_GEOS
    static bool initialized = false;
    static std::vector<std::shared_ptr<Geometry>> geometries;
    static LineView lineView;
    static MeshView meshView;

    if (!initialized)
    {
        // 初始化GEOS适配器
        if (!GeosAdapter::instance().initialize())
        {
            ImGui::Text("Failed to initialize GEOS adapter");
            return;
        }

        // 创建示例几何图形
        geometries.clear();

        // 1. 创建矩形多边形
        auto rect = GeosGeometryFactory::createRectangle(-120.0, 35.0, -110.0, 45.0);
        if (rect) {
            geometries.push_back(rect);
        }

        // 2. 创建圆形多边形
        auto circle = GeosGeometryFactory::createCircle(-100.0, 40.0, 5.0, 32);
        if (circle) {
            geometries.push_back(circle);
        }

        // 3. 创建线串
        std::vector<glm::dvec3> linePoints = {
            {-130.0, 30.0, 0.0},
            {-125.0, 35.0, 0.0},
            {-120.0, 30.0, 0.0},
            {-115.0, 35.0, 0.0},
            {-110.0, 30.0, 0.0}
        };
        auto line = GeosAdapter::instance().createLineString(linePoints);
        if (line) {
            geometries.push_back(line);
        }

        // 4. 创建多点
        std::vector<glm::dvec3> points = {
            {-105.0, 35.0, 0.0},
            {-100.0, 35.0, 0.0},
            {-95.0, 35.0, 0.0},
            {-90.0, 35.0, 0.0}
        };
        auto multiPoint = GeosAdapter::instance().createMultiPoint(points);
        if (multiPoint) {
            geometries.push_back(multiPoint);
        }

        initialized = true;
    }

    ImGui::Text("GEOS Geometry Demo");
    ImGui::Separator();

    ImGui::Text("Created %zu geometries using GEOS library", geometries.size());

    if (ImGui::Button("Test Geometry Operations"))
    {
        if (geometries.size() >= 2)
        {
            // 测试几何运算
            auto geom1 = geometries[0].get();
            auto geom2 = geometries[1].get();

            bool intersects = GeosAdapter::instance().intersects(geom1, geom2);
            bool valid1 = GeosAdapter::instance().isValid(geom1);
            bool valid2 = GeosAdapter::instance().isValid(geom2);

            double area1 = GeosAdapter::instance().getArea(geom1);
            double area2 = GeosAdapter::instance().getArea(geom2);

            Log()->info("Geometry 1: valid={}, area={}", valid1, area1);
            Log()->info("Geometry 2: valid={}, area={}", valid2, area2);
            Log()->info("Geometries intersect: {}", intersects);
        }
    }

    if (ImGui::Button("Export to WKT"))
    {
        for (size_t i = 0; i < geometries.size(); ++i)
        {
            std::string wkt = GeosAdapter::instance().toWKT(geometries[i].get());
            if (!wkt.empty())
            {
                Log()->info("Geometry {}: {}", i, wkt);
            }
        }
    }

    if (ImGui::Button("Create Buffer"))
    {
        if (!geometries.empty())
        {
            auto buffered = GeosAdapter::instance().buffer(geometries[0].get(), 2.0);
            if (buffered)
            {
                geometries.push_back(buffered);
                Log()->info("Created buffer geometry, total geometries: {}", geometries.size());
            }
        }
    }

    if (ImGui::Button("Simplify Geometry"))
    {
        if (!geometries.empty())
        {
            auto simplified = GeosAdapter::instance().simplify(geometries[0].get(), 0.5);
            if (simplified)
            {
                geometries.push_back(simplified);
                Log()->info("Created simplified geometry, total geometries: {}", geometries.size());
            }
        }
    }

    ImGui::Separator();
    ImGui::Text("Geometry Factory Examples:");

    static float rectMinX = -130.0f, rectMinY = 30.0f, rectMaxX = -120.0f, rectMaxY = 40.0f;
    ImGui::SliderFloat("Rect MinX", &rectMinX, -180.0f, 180.0f);
    ImGui::SliderFloat("Rect MinY", &rectMinY, -90.0f, 90.0f);
    ImGui::SliderFloat("Rect MaxX", &rectMaxX, -180.0f, 180.0f);
    ImGui::SliderFloat("Rect MaxY", &rectMaxY, -90.0f, 90.0f);

    if (ImGui::Button("Create Rectangle"))
    {
        auto rect = GeosGeometryFactory::createRectangle(rectMinX, rectMinY, rectMaxX, rectMaxY);
        if (rect)
        {
            geometries.push_back(rect);
            Log()->info("Created rectangle, total geometries: {}", geometries.size());
        }
    }

    static float circleX = -100.0f, circleY = 40.0f, circleRadius = 5.0f;
    static int circleSegments = 32;
    ImGui::SliderFloat("Circle X", &circleX, -180.0f, 180.0f);
    ImGui::SliderFloat("Circle Y", &circleY, -90.0f, 90.0f);
    ImGui::SliderFloat("Circle Radius", &circleRadius, 1.0f, 20.0f);
    ImGui::SliderInt("Circle Segments", &circleSegments, 8, 64);

    if (ImGui::Button("Create Circle"))
    {
        auto circle = GeosGeometryFactory::createCircle(circleX, circleY, circleRadius, circleSegments);
        if (circle)
        {
            geometries.push_back(circle);
            Log()->info("Created circle, total geometries: {}", geometries.size());
        }
    }

    if (ImGui::Button("Clear All Geometries"))
    {
        geometries.clear();
        Log()->info("Cleared all geometries");
    }

    ImGui::Separator();
    ImGui::Text("Note: This demo shows GEOS geometry creation and operations.");
    ImGui::Text("Actual rendering requires integration with Rocky's rendering system.");

#else
    ImGui::Text("GEOS support not compiled in.");
    ImGui::Text("Please rebuild with ROCKY_HAS_GEOS=ON to enable this demo.");
#endif
};
