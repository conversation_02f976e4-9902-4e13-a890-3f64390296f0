/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#pragma once

#include <rocky/rocky.h>
#include <rocky/URI.h>
#include <filesystem>
#include <fstream>
#include <regex>
#include "TileValidator.h"

namespace TileCaching
{
    /**
     * 瓦片缓存拦截器 - 拦截HTTP下载并保存瓦片到缓存目录
     */
    class TileCacheInterceptor
    {
    public:
        TileCacheInterceptor(const std::string& cacheDir) : cacheDir_(cacheDir) 
        {
            std::filesystem::create_directories(cacheDir_);
            std::filesystem::create_directories(cacheDir_ + "/imagery");
            std::filesystem::create_directories(cacheDir_ + "/elevation");
        }

        /**
         * 拦截并缓存瓦片下载
         */
        void interceptTileDownload(const std::string& url, const std::string& data)
        {
            try
            {
                // 解析URL以确定瓦片类型和坐标
                TileInfo tileInfo = parseUrlForTileInfo(url);
                
                if (tileInfo.isValid)
                {
                    // 保存瓦片文件
                    saveTileToCache(tileInfo, data);
                    
                    // 通知验证器
                    if (auto validator = TileValidation::getValidator())
                    {
                        std::vector<uint8_t> dataBytes(data.begin(), data.end());
                        
                        if (tileInfo.isElevation)
                        {
                            validator->recordElevationTile(tileInfo.z, tileInfo.x, tileInfo.y, url, dataBytes);
                        }
                        else
                        {
                            validator->recordImageryTile(tileInfo.z, tileInfo.x, tileInfo.y, url, dataBytes);
                        }
                    }
                }
            }
            catch (const std::exception& e)
            {
                rocky::Log()->warn("[TileCacheInterceptor] Failed to cache tile: {}", e.what());
            }
        }

    private:
        struct TileInfo
        {
            bool isValid = false;
            bool isElevation = false;
            int z = 0, x = 0, y = 0;
            std::string extension;
            std::string filename;
        };

        std::string cacheDir_;

        /**
         * 从URL解析瓦片信息
         */
        TileInfo parseUrlForTileInfo(const std::string& url)
        {
            TileInfo info;
            
            // 匹配不同的瓦片URL模式
            std::vector<std::pair<std::regex, bool>> patterns = {
                // Google Maps影像瓦片
                {std::regex(R"(mt\d*\.google\.com/vt/lyrs=s&x=(\d+)&y=(\d+)&z=(\d+))"), false},
                
                // ReadyMap高程瓦片 (TIFF)
                {std::regex(R"(readymap\.org/readymap/tiles/1\.0\.0/116/(\d+)/(\d+)/(\d+)\.tif)"), true},
                
                // AWS Terrarium高程瓦片 (PNG)
                {std::regex(R"(s3\.amazonaws\.com/elevation-tiles-prod/terrarium/(\d+)/(\d+)/(\d+)\.png)"), true},
                
                // 通用TMS模式 {z}/{x}/{y}.ext
                {std::regex(R"(/(\d+)/(\d+)/(\d+)\.(\w+))"), false}
            };

            for (const auto& [pattern, isElevation] : patterns)
            {
                std::smatch matches;
                if (std::regex_search(url, matches, pattern))
                {
                    info.isValid = true;
                    info.isElevation = isElevation;
                    
                    if (matches.size() >= 4)
                    {
                        if (url.find("google.com") != std::string::npos)
                        {
                            // Google Maps: x, y, z
                            info.x = std::stoi(matches[1].str());
                            info.y = std::stoi(matches[2].str());
                            info.z = std::stoi(matches[3].str());
                            info.extension = "jpg";
                        }
                        else
                        {
                            // 标准TMS: z, x, y
                            info.z = std::stoi(matches[1].str());
                            info.x = std::stoi(matches[2].str());
                            info.y = std::stoi(matches[3].str());
                            
                            if (matches.size() >= 5)
                            {
                                info.extension = matches[4].str();
                            }
                            else if (isElevation)
                            {
                                info.extension = url.find(".png") != std::string::npos ? "png" : "tif";
                            }
                            else
                            {
                                info.extension = "jpg";
                            }
                        }
                        
                        // 生成文件名
                        std::string prefix = info.isElevation ? "elevation" : "imagery";
                        info.filename = prefix + "_z" + std::to_string(info.z) + 
                                       "_x" + std::to_string(info.x) + 
                                       "_y" + std::to_string(info.y) + 
                                       "." + info.extension;
                        
                        break;
                    }
                }
            }
            
            return info;
        }

        /**
         * 保存瓦片到缓存目录
         */
        void saveTileToCache(const TileInfo& info, const std::string& data)
        {
            std::string subdir = info.isElevation ? "elevation" : "imagery";
            std::string filepath = cacheDir_ + "/" + subdir + "/" + info.filename;
            
            std::ofstream file(filepath, std::ios::binary);
            if (file.is_open())
            {
                file.write(data.c_str(), data.size());
                file.close();
                
                rocky::Log()->info("[TileCacheInterceptor] Cached {} tile: {} ({} bytes)", 
                                  subdir, info.filename, data.size());
            }
            else
            {
                rocky::Log()->warn("[TileCacheInterceptor] Failed to save tile: {}", filepath);
            }
        }
    };

    // 全局缓存拦截器实例
    inline std::unique_ptr<TileCacheInterceptor> g_cacheInterceptor;
    
    // 初始化缓存拦截器
    inline void initializeCacheInterceptor(const std::string& cacheDir)
    {
        g_cacheInterceptor = std::make_unique<TileCacheInterceptor>(cacheDir);
    }
    
    // 获取缓存拦截器实例
    inline TileCacheInterceptor* getCacheInterceptor()
    {
        return g_cacheInterceptor.get();
    }
}

// 全局函数用于拦截HTTP响应
namespace rocky
{
    // 声明一个全局回调函数，用于拦截HTTP下载
    inline void interceptHttpResponse(const std::string& url, const std::string& data)
    {
        if (auto interceptor = TileCaching::getCacheInterceptor())
        {
            interceptor->interceptTileDownload(url, data);
        }
    }
}
