﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{13D36099-4561-34F2-8E47-931F5DD7E330}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>rocky_demo</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">rocky_demo.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">rocky_demo</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">rocky_demo.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">rocky_demo</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">rocky_demo.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">rocky_demo</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">rocky_demo.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">rocky_demo</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;ROCKY_BUILDING_SDK;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;VSG_SHARED_LIBRARY;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;ROCKY_BUILDING_SDK;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;VSG_SHARED_LIBRARY;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/Debug/rocky_demo.exe -installedDir C:/dev/vcpkg/installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\rocky\Debug\rocky.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\imguid.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\glm.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\spdlogd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\fmtd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\vsgd.lib;C:\dev\vcpkg\installed\x64-windows\lib\vulkan-1.lib;C:\dev\vcpkg\installed\x64-windows\lib\vulkan-1.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/Debug/rocky_demo.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/Debug/rocky_demo.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;VSG_SHARED_LIBRARY;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;VSG_SHARED_LIBRARY;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/Release/rocky_demo.exe -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\rocky\Release\rocky.lib;C:\dev\vcpkg\installed\x64-windows\lib\imgui.lib;C:\dev\vcpkg\installed\x64-windows\lib\glm.lib;C:\dev\vcpkg\installed\x64-windows\lib\spdlog.lib;C:\dev\vcpkg\installed\x64-windows\lib\fmt.lib;C:\dev\vcpkg\installed\x64-windows\lib\vsg.lib;C:\dev\vcpkg\installed\x64-windows\lib\vulkan-1.lib;C:\dev\vcpkg\installed\x64-windows\lib\vulkan-1.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/Release/rocky_demo.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/Release/rocky_demo.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;VSG_SHARED_LIBRARY;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;VSG_SHARED_LIBRARY;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/MinSizeRel/rocky_demo.exe -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\rocky\MinSizeRel\rocky.lib;C:\dev\vcpkg\installed\x64-windows\lib\imgui.lib;C:\dev\vcpkg\installed\x64-windows\lib\glm.lib;C:\dev\vcpkg\installed\x64-windows\lib\spdlog.lib;C:\dev\vcpkg\installed\x64-windows\lib\fmt.lib;C:\dev\vcpkg\installed\x64-windows\lib\vsg.lib;C:\dev\vcpkg\installed\x64-windows\lib\vulkan-1.lib;C:\dev\vcpkg\installed\x64-windows\lib\vulkan-1.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/MinSizeRel/rocky_demo.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/MinSizeRel/rocky_demo.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;VSG_SHARED_LIBRARY;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;VSG_SHARED_LIBRARY;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;C:\dev\vcpkg\installed\x64-windows\include\imgui;C:\dev\vcpkg\installed\x64-windows\include\imgui\backends;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/RelWithDebInfo/rocky_demo.exe -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\rocky\RelWithDebInfo\rocky.lib;C:\dev\vcpkg\installed\x64-windows\lib\imgui.lib;C:\dev\vcpkg\installed\x64-windows\lib\glm.lib;C:\dev\vcpkg\installed\x64-windows\lib\spdlog.lib;C:\dev\vcpkg\installed\x64-windows\lib\fmt.lib;C:\dev\vcpkg\installed\x64-windows\lib\vsg.lib;C:\dev\vcpkg\installed\x64-windows\lib\vulkan-1.lib;C:\dev\vcpkg\installed\x64-windows\lib\vulkan-1.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/RelWithDebInfo/rocky_demo.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/RelWithDebInfo/rocky_demo.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/rocky/src/apps/rocky_demo/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/rocky/src/apps/rocky_demo/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/rocky/src/apps/rocky_demo/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/rocky/src/apps/rocky_demo/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/apps/rocky_demo/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\rocky_demo.cpp" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Decluttering.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Environment.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Geocoder.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_GeosGeometry.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Icon.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Label.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_LabelFeatures.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Line.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_LineFeatures.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Map.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_MapManipulator.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Mesh.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Model.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_NodePager.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_PolygonFeatures.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_RTT.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Registry.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Rendering.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Serialization.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Simulation.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Stats.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Tethering.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_TrackHistory.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Views.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\Demo_Widget.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\ElevationValidator.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\TileCacheInterceptor.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\TileValidator.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\helpers.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\rocky\build_desk\ZERO_CHECK.vcxproj">
      <Project>{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\rocky.vcxproj">
      <Project>{DA118014-FAC1-33E8-B89B-766EAA796615}</Project>
      <Name>rocky</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>