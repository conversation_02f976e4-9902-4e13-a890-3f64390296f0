#include <rocky/SRS.h>
#include <rocky/GeoPoint.h>
#include <rocky/Profile.h>
#include <rocky/TileKey.h>
#include <iostream>
#include <iomanip>
#include <cmath>

using namespace rocky;

// 验证Web Mercator关键坐标点
bool validateWebMercatorTransforms()
{
    std::cout << "\n=== Web Mercator 关键坐标转换验证 ===" << std::endl;

    SRS wgs84("epsg:4326");
    SRS webMercator("epsg:3857");

    if (!wgs84.valid() || !webMercator.valid())
    {
        std::cout << "❌ 坐标系创建失败" << std::endl;
        return false;
    }

    // 关键测试点 - 这些是Web地图瓦片系统的关键边界点
    struct TestPoint
    {
        double lon, lat;
        double expectedX, expectedY;
        std::string description;
    };

    TestPoint testPoints[] = {
        // Web Mercator的关键边界点
        {-180.0, 0.0, -20037508.342789244, 0.0, "西边界"},
        {180.0, 0.0, 20037508.342789244, 0.0, "东边界"},
        {0.0, 85.0511287798, 0.0, 20037508.342789244, "北边界(约85.05度)"},
        {0.0, -85.0511287798, 0.0, -20037508.342789244, "南边界(约-85.05度)"},
        {0.0, 0.0, 0.0, 0.0, "原点"},
        // 常用城市坐标
        {116.4074, 39.9042, 12958129.206, 4825923.220, "北京"},
        {-74.0060, 40.7128, -8238310.240, 4969803.890, "纽约"}};

    bool allPassed = true;
    double tolerance = 1.0; // 1米误差容忍

    std::cout << std::fixed << std::setprecision(3);

    for (const auto &tp : testPoints)
    {
        GeoPoint wgsPoint(wgs84, tp.lon, tp.lat, 0.0);
        GeoPoint mercatorPoint = wgsPoint.transform(webMercator);

        double xError = std::abs(mercatorPoint.x - tp.expectedX);
        double yError = std::abs(mercatorPoint.y - tp.expectedY);

        std::cout << tp.description << ": ";
        std::cout << "(" << tp.lon << "°, " << tp.lat << "°) -> ";
        std::cout << "(" << mercatorPoint.x << ", " << mercatorPoint.y << ")";

        if (xError < tolerance && yError < tolerance)
        {
            std::cout << " ✅" << std::endl;
        }
        else
        {
            std::cout << " ❌ (误差: X=" << xError << "m, Y=" << yError << "m)" << std::endl;
            std::cout << "    期望: (" << tp.expectedX << ", " << tp.expectedY << ")" << std::endl;
            allPassed = false;
        }
    }

    return allPassed;
}

// 验证瓦片坐标计算
bool validateTileCoordinates()
{
    std::cout << "\n=== 瓦片坐标计算验证 ===" << std::endl;

    // 测试spherical-mercator profile
    Profile mercatorProfile("spherical-mercator");
    if (!mercatorProfile.valid())
    {
        std::cout << "❌ Spherical Mercator Profile创建失败" << std::endl;
        return false;
    }

    // 测试global-geodetic profile
    Profile geodeticProfile("global-geodetic");
    if (!geodeticProfile.valid())
    {
        std::cout << "❌ Global Geodetic Profile创建失败" << std::endl;
        return false;
    }

    std::cout << "✅ Profile创建成功" << std::endl;
    std::cout << "Spherical Mercator SRS: " << mercatorProfile.srs().name() << std::endl;
    std::cout << "Global Geodetic SRS: " << geodeticProfile.srs().name() << std::endl;

    // 验证边界
    auto mercatorExtent = mercatorProfile.extent();
    auto geodeticExtent = geodeticProfile.extent();

    std::cout << std::fixed << std::setprecision(6);
    std::cout << "Mercator范围: " << mercatorExtent.xmin() << ", " << mercatorExtent.ymin()
              << " to " << mercatorExtent.xmax() << ", " << mercatorExtent.ymax() << std::endl;
    std::cout << "Geodetic范围: " << geodeticExtent.xmin() << ", " << geodeticExtent.ymin()
              << " to " << geodeticExtent.xmax() << ", " << geodeticExtent.ymax() << std::endl;

    // 验证瓦片数量计算
    std::cout << "\n瓦片数量验证:" << std::endl;
    for (unsigned lod = 0; lod <= 3; ++lod)
    {
        auto mercatorTiles = mercatorProfile.numTiles(lod);
        auto geodeticTiles = geodeticProfile.numTiles(lod);

        std::cout << "LOD " << lod << " - 瓦片数量:" << std::endl;
        std::cout << "  Mercator: " << mercatorTiles.first << " x " << mercatorTiles.second << std::endl;
        std::cout << "  Geodetic: " << geodeticTiles.first << " x " << geodeticTiles.second << std::endl;
    }

    return true;
}

// 验证往返转换精度
bool validateRoundTripAccuracy()
{
    std::cout << "\n=== 往返转换精度验证 ===" << std::endl;

    SRS wgs84("wgs84");
    SRS webMercator("spherical-mercator");

    // 测试关键区域的往返转换
    struct TestRegion
    {
        double minLon, minLat, maxLon, maxLat;
        std::string name;
    };

    TestRegion regions[] = {
        {-180, -85, 180, 85, "全球范围"},
        {110, 30, 120, 40, "中国东部"},
        {-80, 35, -70, 45, "美国东海岸"},
        {0, 45, 10, 55, "欧洲西部"}};

    bool allPassed = true;
    double tolerance = 1e-9; // 纳度级精度

    for (const auto &region : regions)
    {
        std::cout << region.name << ": ";

        // 测试区域的四个角点
        GeoPoint corners[] = {
            GeoPoint(wgs84, region.minLon, region.minLat, 0.0),
            GeoPoint(wgs84, region.maxLon, region.minLat, 0.0),
            GeoPoint(wgs84, region.minLon, region.maxLat, 0.0),
            GeoPoint(wgs84, region.maxLon, region.maxLat, 0.0)};

        double maxError = 0.0;
        for (const auto &corner : corners)
        {
            GeoPoint mercator = corner.transform(webMercator);
            GeoPoint backToWgs = mercator.transform(wgs84);

            double lonError = std::abs(backToWgs.x - corner.x);
            double latError = std::abs(backToWgs.y - corner.y);
            maxError = std::max(maxError, std::max(lonError, latError));
        }

        if (maxError < tolerance)
        {
            std::cout << "✅ (最大误差: " << std::scientific << std::setprecision(2) << maxError << "°)" << std::endl;
        }
        else
        {
            std::cout << "❌ (最大误差: " << std::scientific << std::setprecision(2) << maxError << "°)" << std::endl;
            allPassed = false;
        }
    }

    return allPassed;
}

// 验证SRS属性和边界
bool validateSRSProperties()
{
    std::cout << "\n=== SRS属性和边界验证 ===" << std::endl;

    // 验证预定义的SRS常量
    std::cout << "预定义SRS验证:" << std::endl;
    std::cout << "WGS84: " << (SRS::WGS84.valid() ? "✅" : "❌") << " " << SRS::WGS84.name() << std::endl;
    std::cout << "SPHERICAL_MERCATOR: " << (SRS::SPHERICAL_MERCATOR.valid() ? "✅" : "❌") << " " << SRS::SPHERICAL_MERCATOR.name() << std::endl;
    std::cout << "ECEF: " << (SRS::ECEF.valid() ? "✅" : "❌") << " " << SRS::ECEF.name() << std::endl;

    // 验证边界 - 使用Profile来获取边界
    Profile mercatorProfile("spherical-mercator");
    Profile wgs84Profile("global-geodetic");
    auto mercatorBounds = mercatorProfile.extent();
    auto wgs84Bounds = wgs84Profile.extent();

    std::cout << std::fixed << std::setprecision(6);
    std::cout << "\n边界验证:" << std::endl;
    std::cout << "WGS84边界: " << wgs84Bounds.xmin() << ", " << wgs84Bounds.ymin()
              << " to " << wgs84Bounds.xmax() << ", " << wgs84Bounds.ymax() << std::endl;
    std::cout << "Mercator边界: " << mercatorBounds.xmin() << ", " << mercatorBounds.ymin()
              << " to " << mercatorBounds.xmax() << ", " << mercatorBounds.ymax() << std::endl;

    // 验证期望的边界值
    bool boundsCorrect = true;
    if (std::abs(wgs84Bounds.xmin() + 180.0) > 0.001 || std::abs(wgs84Bounds.xmax() - 180.0) > 0.001)
    {
        std::cout << "❌ WGS84经度边界不正确" << std::endl;
        boundsCorrect = false;
    }
    if (std::abs(mercatorBounds.xmin() + 20037508.342789244) > 1.0 || std::abs(mercatorBounds.xmax() - 20037508.342789244) > 1.0)
    {
        std::cout << "❌ Mercator X边界不正确" << std::endl;
        boundsCorrect = false;
    }

    if (boundsCorrect)
    {
        std::cout << "✅ 边界值正确" << std::endl;
    }

    return boundsCorrect;
}

int main()
{
    std::cout << "Rocky SRS 坐标转换正确性验证" << std::endl;
    std::cout << "专门验证图层加载所需的关键坐标转换" << std::endl;

    int passedTests = 0;
    int totalTests = 4;

    if (validateSRSProperties())
        passedTests++;
    if (validateWebMercatorTransforms())
        passedTests++;
    if (validateTileCoordinates())
        passedTests++;
    if (validateRoundTripAccuracy())
        passedTests++;

    std::cout << "\n"
              << std::string(60, '=') << std::endl;
    std::cout << "验证结果: " << passedTests << "/" << totalTests << " 通过" << std::endl;

    if (passedTests == totalTests)
    {
        std::cout << "🎉 所有关键坐标转换验证通过！" << std::endl;
        std::cout << "GeographicLib重构与原PROJ实现兼容！" << std::endl;
        return 0;
    }
    else
    {
        std::cout << "⚠️  发现坐标转换问题，需要修复" << std::endl;
        return 1;
    }
}
