=============================================================================
== @PROJECT_NAME@ 
==    is a library for generating TIFF-files                              ==
=============================================================================

This is a lightweight C/C++ library, which is able to read and write basic TIFF
files. It is significantly faster than libTIFF, especially in writing large
multi-frame TIFFs.

Library Information:
  version:                @PROJECT_VERSION@
  main author/maintainer: <PERSON> <<EMAIL>>
  copyright:              (c) 2014-2024 by Jan Krieger
  license:                GNU LESSER GENERAL PUBLIC LICENSE >= v3.0 (see https://github.com/jkriege2/TinyTIFF/LICENSE)
  repository:             https://github.com/jkriege2/TinyTIFF
  documentation:          http://jkriege2.github.io/TinyTIFF/index.html

Build information:
  used compiler:          @CMAKE_CXX_COMPILER_ID@ @CMAKE_CXX_COMPILER_VERSION@
  built static libs:      @BUILD_STATIC_LIBS@
  built shared libs:      @BUILD_SHARED_LIBS@
  built examples:         @BUILD_EXAMPLES@


