cmake_minimum_required(VERSION 3.20)
project(srs_validation)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译器选项
if(MSVC)
    add_compile_options(/utf-8)
endif()

# 使用redist_desk中的Rocky库
set(ROCKY_INCLUDE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/include")
set(ROCKY_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/lib")
set(ROCKY_BIN_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/bin")

# 创建验证程序
add_executable(srs_validation srs_validation.cpp)

# 设置包含目录
target_include_directories(srs_validation PRIVATE ${ROCKY_INCLUDE_DIR})

# 链接Rocky库
target_link_libraries(srs_validation PRIVATE "${ROCKY_LIB_DIR}/rocky.lib")

# 复制运行时依赖
if(WIN32)
    add_custom_command(TARGET srs_validation POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${ROCKY_BIN_DIR}/rocky.dll"
        $<TARGET_FILE_DIR:srs_validation>)
        
    add_custom_command(TARGET srs_validation POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "C:/dev/vcpkg/installed/x64-windows/bin/GeographicLib.dll"
        $<TARGET_FILE_DIR:srs_validation>)
endif()
