﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{8913F0AA-1790-3DD6-A332-24666919F5AA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "apps", "apps", "{E49BCAD1-DB91-3B3B-A9DE-A2D023670E28}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "rocky", "rocky", "{B15DD935-5D3B-340E-9A29-65E5773E1054}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{DE6D8138-01AA-381C-A9A7-383166B6FEA0}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59}"
	ProjectSection(ProjectDependencies) = postProject
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1} = {78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}
		{DA118014-FAC1-33E8-B89B-766EAA796615} = {DA118014-FAC1-33E8-B89B-766EAA796615}
		{13D36099-4561-34F2-8E47-931F5DD7E330} = {13D36099-4561-34F2-8E47-931F5DD7E330}
		{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B} = {9EF28049-C6FD-3CFC-9F59-E1A84EEC888B}
		{830E910F-27C5-3961-9BC0-9E917DE5C265} = {830E910F-27C5-3961-9BC0-9E917DE5C265}
		{44479BB3-EF01-3C25-8E84-1FCEF8B8388F} = {44479BB3-EF01-3C25-8E84-1FCEF8B8388F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{216B07AF-3FA1-32C5-9A95-9316E22CB407}"
	ProjectSection(ProjectDependencies) = postProject
		{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59} = {2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59}
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1} = {78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky", "src\rocky\rocky.vcxproj", "{DA118014-FAC1-33E8-B89B-766EAA796615}"
	ProjectSection(ProjectDependencies) = postProject
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1} = {78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky_demo", "src\apps\rocky_demo\rocky_demo.vcxproj", "{13D36099-4561-34F2-8E47-931F5DD7E330}"
	ProjectSection(ProjectDependencies) = postProject
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1} = {78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}
		{DA118014-FAC1-33E8-B89B-766EAA796615} = {DA118014-FAC1-33E8-B89B-766EAA796615}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky_engine", "src\apps\rocky_engine\rocky_engine.vcxproj", "{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B}"
	ProjectSection(ProjectDependencies) = postProject
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1} = {78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}
		{DA118014-FAC1-33E8-B89B-766EAA796615} = {DA118014-FAC1-33E8-B89B-766EAA796615}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky_simple", "src\apps\rocky_simple\rocky_simple.vcxproj", "{830E910F-27C5-3961-9BC0-9E917DE5C265}"
	ProjectSection(ProjectDependencies) = postProject
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1} = {78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}
		{DA118014-FAC1-33E8-B89B-766EAA796615} = {DA118014-FAC1-33E8-B89B-766EAA796615}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rocky_tests", "src\tests\rocky_tests.vcxproj", "{44479BB3-EF01-3C25-8E84-1FCEF8B8388F}"
	ProjectSection(ProjectDependencies) = postProject
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1} = {78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}
		{DA118014-FAC1-33E8-B89B-766EAA796615} = {DA118014-FAC1-33E8-B89B-766EAA796615}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59}.Debug|x64.ActiveCfg = Debug|x64
		{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59}.Debug|x64.Build.0 = Debug|x64
		{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59}.Release|x64.ActiveCfg = Release|x64
		{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59}.Release|x64.Build.0 = Release|x64
		{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{216B07AF-3FA1-32C5-9A95-9316E22CB407}.Debug|x64.ActiveCfg = Debug|x64
		{216B07AF-3FA1-32C5-9A95-9316E22CB407}.Release|x64.ActiveCfg = Release|x64
		{216B07AF-3FA1-32C5-9A95-9316E22CB407}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{216B07AF-3FA1-32C5-9A95-9316E22CB407}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}.Debug|x64.ActiveCfg = Debug|x64
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}.Debug|x64.Build.0 = Debug|x64
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}.Release|x64.ActiveCfg = Release|x64
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}.Release|x64.Build.0 = Release|x64
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{DA118014-FAC1-33E8-B89B-766EAA796615}.Debug|x64.ActiveCfg = Debug|x64
		{DA118014-FAC1-33E8-B89B-766EAA796615}.Debug|x64.Build.0 = Debug|x64
		{DA118014-FAC1-33E8-B89B-766EAA796615}.Release|x64.ActiveCfg = Release|x64
		{DA118014-FAC1-33E8-B89B-766EAA796615}.Release|x64.Build.0 = Release|x64
		{DA118014-FAC1-33E8-B89B-766EAA796615}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DA118014-FAC1-33E8-B89B-766EAA796615}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{DA118014-FAC1-33E8-B89B-766EAA796615}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DA118014-FAC1-33E8-B89B-766EAA796615}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{13D36099-4561-34F2-8E47-931F5DD7E330}.Debug|x64.ActiveCfg = Debug|x64
		{13D36099-4561-34F2-8E47-931F5DD7E330}.Debug|x64.Build.0 = Debug|x64
		{13D36099-4561-34F2-8E47-931F5DD7E330}.Release|x64.ActiveCfg = Release|x64
		{13D36099-4561-34F2-8E47-931F5DD7E330}.Release|x64.Build.0 = Release|x64
		{13D36099-4561-34F2-8E47-931F5DD7E330}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{13D36099-4561-34F2-8E47-931F5DD7E330}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{13D36099-4561-34F2-8E47-931F5DD7E330}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{13D36099-4561-34F2-8E47-931F5DD7E330}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B}.Debug|x64.ActiveCfg = Debug|x64
		{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B}.Debug|x64.Build.0 = Debug|x64
		{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B}.Release|x64.ActiveCfg = Release|x64
		{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B}.Release|x64.Build.0 = Release|x64
		{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{830E910F-27C5-3961-9BC0-9E917DE5C265}.Debug|x64.ActiveCfg = Debug|x64
		{830E910F-27C5-3961-9BC0-9E917DE5C265}.Debug|x64.Build.0 = Debug|x64
		{830E910F-27C5-3961-9BC0-9E917DE5C265}.Release|x64.ActiveCfg = Release|x64
		{830E910F-27C5-3961-9BC0-9E917DE5C265}.Release|x64.Build.0 = Release|x64
		{830E910F-27C5-3961-9BC0-9E917DE5C265}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{830E910F-27C5-3961-9BC0-9E917DE5C265}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{830E910F-27C5-3961-9BC0-9E917DE5C265}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{830E910F-27C5-3961-9BC0-9E917DE5C265}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{44479BB3-EF01-3C25-8E84-1FCEF8B8388F}.Debug|x64.ActiveCfg = Debug|x64
		{44479BB3-EF01-3C25-8E84-1FCEF8B8388F}.Debug|x64.Build.0 = Debug|x64
		{44479BB3-EF01-3C25-8E84-1FCEF8B8388F}.Release|x64.ActiveCfg = Release|x64
		{44479BB3-EF01-3C25-8E84-1FCEF8B8388F}.Release|x64.Build.0 = Release|x64
		{44479BB3-EF01-3C25-8E84-1FCEF8B8388F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{44479BB3-EF01-3C25-8E84-1FCEF8B8388F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{44479BB3-EF01-3C25-8E84-1FCEF8B8388F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{44479BB3-EF01-3C25-8E84-1FCEF8B8388F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{2F60C9EA-DB34-3F02-9DDC-C8310C1D1F59} = {8913F0AA-1790-3DD6-A332-24666919F5AA}
		{216B07AF-3FA1-32C5-9A95-9316E22CB407} = {8913F0AA-1790-3DD6-A332-24666919F5AA}
		{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1} = {8913F0AA-1790-3DD6-A332-24666919F5AA}
		{13D36099-4561-34F2-8E47-931F5DD7E330} = {E49BCAD1-DB91-3B3B-A9DE-A2D023670E28}
		{9EF28049-C6FD-3CFC-9F59-E1A84EEC888B} = {E49BCAD1-DB91-3B3B-A9DE-A2D023670E28}
		{830E910F-27C5-3961-9BC0-9E917DE5C265} = {E49BCAD1-DB91-3B3B-A9DE-A2D023670E28}
		{DA118014-FAC1-33E8-B89B-766EAA796615} = {B15DD935-5D3B-340E-9A29-65E5773E1054}
		{44479BB3-EF01-3C25-8E84-1FCEF8B8388F} = {DE6D8138-01AA-381C-A9A7-383166B6FEA0}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {33DDF89F-D19A-3FC4-BD8A-05F3B376E220}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
