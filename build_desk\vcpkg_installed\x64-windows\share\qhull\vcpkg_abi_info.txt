cmake 3.30.1
features core
fix-missing-symbols.patch 9607e9ff10c3038c1761f9937522e97f493eb3154850aaefdfe1f05a2759f698
fix-qhullcpp-cpp20-support.patch 9cc1e933cd4542a1c22c1a42bd99ade5d451bde0e2eeb9f2dcfaeb59a2c82eb1
include-qhullcpp-shared.patch 016361966d98caa31a72ca7417f23a8e9b558a6d61a51334ee4ebb8c13b05ee7
noapp.patch aa31ef0e5272dee98ac96c394c80d714c1995b51a79d772d02fee86d31015b6a
portfile.cmake bd26aee8aa6b06fe1be31bee27668e95b7884ef094a317a01e5bfd0e346d698b
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
usage 7d4172f39a0d94a5a1944906f467013fa43896f082996d868bc6340fa4eb3970
vcpkg-cmake fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
vcpkg-cmake-config 8d261494f5cab6cecd7851edc7eb14f27f3c82cd259e8a56095998d1199973a0
vcpkg.json d373f04638375693571d305f181d6f17f4dcea7e5032085ce400305cd38fa73f
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_fixup_pkgconfig 1a15f6c6d8e2b244d83a7514a0412d339127d2217d1df60ad1388b546c85f777
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
