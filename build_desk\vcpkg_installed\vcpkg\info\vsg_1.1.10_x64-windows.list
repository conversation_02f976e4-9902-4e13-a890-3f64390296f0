x64-windows/
x64-windows/bin/
x64-windows/bin/vsg-14.dll
x64-windows/bin/vsg-14.pdb
x64-windows/debug/
x64-windows/debug/bin/
x64-windows/debug/bin/vsg-14d.dll
x64-windows/debug/bin/vsg-14d.pdb
x64-windows/debug/lib/
x64-windows/debug/lib/vsgd.lib
x64-windows/include/
x64-windows/include/vsg/
x64-windows/include/vsg/all.h
x64-windows/include/vsg/animation/
x64-windows/include/vsg/animation/Animation.h
x64-windows/include/vsg/animation/AnimationGroup.h
x64-windows/include/vsg/animation/AnimationManager.h
x64-windows/include/vsg/animation/CameraAnimationHandler.h
x64-windows/include/vsg/animation/CameraSampler.h
x64-windows/include/vsg/animation/FindAnimations.h
x64-windows/include/vsg/animation/Joint.h
x64-windows/include/vsg/animation/JointSampler.h
x64-windows/include/vsg/animation/MorphSampler.h
x64-windows/include/vsg/animation/TransformSampler.h
x64-windows/include/vsg/animation/time_value.h
x64-windows/include/vsg/app/
x64-windows/include/vsg/app/Camera.h
x64-windows/include/vsg/app/CloseHandler.h
x64-windows/include/vsg/app/CommandGraph.h
x64-windows/include/vsg/app/CompileManager.h
x64-windows/include/vsg/app/CompileTraversal.h
x64-windows/include/vsg/app/EllipsoidModel.h
x64-windows/include/vsg/app/Presentation.h
x64-windows/include/vsg/app/ProjectionMatrix.h
x64-windows/include/vsg/app/RecordAndSubmitTask.h
x64-windows/include/vsg/app/RecordTraversal.h
x64-windows/include/vsg/app/RenderGraph.h
x64-windows/include/vsg/app/SecondaryCommandGraph.h
x64-windows/include/vsg/app/Trackball.h
x64-windows/include/vsg/app/TransferTask.h
x64-windows/include/vsg/app/UpdateOperations.h
x64-windows/include/vsg/app/View.h
x64-windows/include/vsg/app/ViewMatrix.h
x64-windows/include/vsg/app/Viewer.h
x64-windows/include/vsg/app/Window.h
x64-windows/include/vsg/app/WindowAdapter.h
x64-windows/include/vsg/app/WindowResizeHandler.h
x64-windows/include/vsg/app/WindowTraits.h
x64-windows/include/vsg/commands/
x64-windows/include/vsg/commands/BeginQuery.h
x64-windows/include/vsg/commands/BindIndexBuffer.h
x64-windows/include/vsg/commands/BindVertexBuffers.h
x64-windows/include/vsg/commands/BlitImage.h
x64-windows/include/vsg/commands/ClearAttachments.h
x64-windows/include/vsg/commands/ClearImage.h
x64-windows/include/vsg/commands/Command.h
x64-windows/include/vsg/commands/Commands.h
x64-windows/include/vsg/commands/CopyAndReleaseBuffer.h
x64-windows/include/vsg/commands/CopyAndReleaseImage.h
x64-windows/include/vsg/commands/CopyImage.h
x64-windows/include/vsg/commands/CopyImageToBuffer.h
x64-windows/include/vsg/commands/CopyImageViewToWindow.h
x64-windows/include/vsg/commands/CopyQueryPoolResults.h
x64-windows/include/vsg/commands/Dispatch.h
x64-windows/include/vsg/commands/Draw.h
x64-windows/include/vsg/commands/DrawIndexed.h
x64-windows/include/vsg/commands/DrawIndexedIndirect.h
x64-windows/include/vsg/commands/DrawIndirect.h
x64-windows/include/vsg/commands/DrawIndirectCommand.h
x64-windows/include/vsg/commands/EndQuery.h
x64-windows/include/vsg/commands/Event.h
x64-windows/include/vsg/commands/ExecuteCommands.h
x64-windows/include/vsg/commands/NextSubPass.h
x64-windows/include/vsg/commands/PipelineBarrier.h
x64-windows/include/vsg/commands/ResetQueryPool.h
x64-windows/include/vsg/commands/ResolveImage.h
x64-windows/include/vsg/commands/SetDepthBias.h
x64-windows/include/vsg/commands/SetLineWidth.h
x64-windows/include/vsg/commands/SetPrimitiveTopology.h
x64-windows/include/vsg/commands/SetScissor.h
x64-windows/include/vsg/commands/SetViewport.h
x64-windows/include/vsg/commands/WriteTimestamp.h
x64-windows/include/vsg/core/
x64-windows/include/vsg/core/Allocator.h
x64-windows/include/vsg/core/Array.h
x64-windows/include/vsg/core/Array2D.h
x64-windows/include/vsg/core/Array3D.h
x64-windows/include/vsg/core/Auxiliary.h
x64-windows/include/vsg/core/ConstVisitor.h
x64-windows/include/vsg/core/Data.h
x64-windows/include/vsg/core/Exception.h
x64-windows/include/vsg/core/Export.h
x64-windows/include/vsg/core/External.h
x64-windows/include/vsg/core/Inherit.h
x64-windows/include/vsg/core/IntrusiveAllocator.h
x64-windows/include/vsg/core/Mask.h
x64-windows/include/vsg/core/MemorySlots.h
x64-windows/include/vsg/core/Object.h
x64-windows/include/vsg/core/Objects.h
x64-windows/include/vsg/core/ScratchMemory.h
x64-windows/include/vsg/core/Value.h
x64-windows/include/vsg/core/Version.h
x64-windows/include/vsg/core/Visitor.h
x64-windows/include/vsg/core/compare.h
x64-windows/include/vsg/core/contains.h
x64-windows/include/vsg/core/observer_ptr.h
x64-windows/include/vsg/core/ref_ptr.h
x64-windows/include/vsg/core/type_name.h
x64-windows/include/vsg/core/visit.h
x64-windows/include/vsg/io/
x64-windows/include/vsg/io/AsciiInput.h
x64-windows/include/vsg/io/AsciiOutput.h
x64-windows/include/vsg/io/BinaryInput.h
x64-windows/include/vsg/io/BinaryOutput.h
x64-windows/include/vsg/io/DatabasePager.h
x64-windows/include/vsg/io/FileSystem.h
x64-windows/include/vsg/io/Input.h
x64-windows/include/vsg/io/Logger.h
x64-windows/include/vsg/io/ObjectFactory.h
x64-windows/include/vsg/io/Options.h
x64-windows/include/vsg/io/Output.h
x64-windows/include/vsg/io/Path.h
x64-windows/include/vsg/io/ReaderWriter.h
x64-windows/include/vsg/io/VSG.h
x64-windows/include/vsg/io/convert_utf.h
x64-windows/include/vsg/io/glsl.h
x64-windows/include/vsg/io/mem_stream.h
x64-windows/include/vsg/io/read.h
x64-windows/include/vsg/io/read_line.h
x64-windows/include/vsg/io/spirv.h
x64-windows/include/vsg/io/stream.h
x64-windows/include/vsg/io/tile.h
x64-windows/include/vsg/io/txt.h
x64-windows/include/vsg/io/write.h
x64-windows/include/vsg/lighting/
x64-windows/include/vsg/lighting/AmbientLight.h
x64-windows/include/vsg/lighting/DirectionalLight.h
x64-windows/include/vsg/lighting/HardShadows.h
x64-windows/include/vsg/lighting/Light.h
x64-windows/include/vsg/lighting/PercentageCloserSoftShadows.h
x64-windows/include/vsg/lighting/PointLight.h
x64-windows/include/vsg/lighting/ShadowSettings.h
x64-windows/include/vsg/lighting/SoftShadows.h
x64-windows/include/vsg/lighting/SpotLight.h
x64-windows/include/vsg/maths/
x64-windows/include/vsg/maths/box.h
x64-windows/include/vsg/maths/clamp.h
x64-windows/include/vsg/maths/color.h
x64-windows/include/vsg/maths/common.h
x64-windows/include/vsg/maths/mat3.h
x64-windows/include/vsg/maths/mat4.h
x64-windows/include/vsg/maths/plane.h
x64-windows/include/vsg/maths/quat.h
x64-windows/include/vsg/maths/sample.h
x64-windows/include/vsg/maths/sphere.h
x64-windows/include/vsg/maths/transform.h
x64-windows/include/vsg/maths/vec2.h
x64-windows/include/vsg/maths/vec3.h
x64-windows/include/vsg/maths/vec4.h
x64-windows/include/vsg/meshshaders/
x64-windows/include/vsg/meshshaders/DrawMeshTasks.h
x64-windows/include/vsg/meshshaders/DrawMeshTasksIndirect.h
x64-windows/include/vsg/meshshaders/DrawMeshTasksIndirectCount.h
x64-windows/include/vsg/nodes/
x64-windows/include/vsg/nodes/AbsoluteTransform.h
x64-windows/include/vsg/nodes/Bin.h
x64-windows/include/vsg/nodes/Compilable.h
x64-windows/include/vsg/nodes/CoordinateFrame.h
x64-windows/include/vsg/nodes/CullGroup.h
x64-windows/include/vsg/nodes/CullNode.h
x64-windows/include/vsg/nodes/DepthSorted.h
x64-windows/include/vsg/nodes/Geometry.h
x64-windows/include/vsg/nodes/Group.h
x64-windows/include/vsg/nodes/InstrumentationNode.h
x64-windows/include/vsg/nodes/LOD.h
x64-windows/include/vsg/nodes/Layer.h
x64-windows/include/vsg/nodes/MatrixTransform.h
x64-windows/include/vsg/nodes/Node.h
x64-windows/include/vsg/nodes/PagedLOD.h
x64-windows/include/vsg/nodes/QuadGroup.h
x64-windows/include/vsg/nodes/RegionOfInterest.h
x64-windows/include/vsg/nodes/StateGroup.h
x64-windows/include/vsg/nodes/Switch.h
x64-windows/include/vsg/nodes/TileDatabase.h
x64-windows/include/vsg/nodes/Transform.h
x64-windows/include/vsg/nodes/VertexDraw.h
x64-windows/include/vsg/nodes/VertexIndexDraw.h
x64-windows/include/vsg/platform/
x64-windows/include/vsg/platform/android/
x64-windows/include/vsg/platform/android/Android_Window.h
x64-windows/include/vsg/platform/ios/
x64-windows/include/vsg/platform/ios/iOS_ViewController.h
x64-windows/include/vsg/platform/ios/iOS_Window.h
x64-windows/include/vsg/platform/macos/
x64-windows/include/vsg/platform/macos/MacOS_Window.h
x64-windows/include/vsg/platform/win32/
x64-windows/include/vsg/platform/win32/Win32_Window.h
x64-windows/include/vsg/platform/xcb/
x64-windows/include/vsg/platform/xcb/Xcb_Window.h
x64-windows/include/vsg/raytracing/
x64-windows/include/vsg/raytracing/AccelerationGeometry.h
x64-windows/include/vsg/raytracing/AccelerationStructure.h
x64-windows/include/vsg/raytracing/BottomLevelAccelerationStructure.h
x64-windows/include/vsg/raytracing/BuildAccelerationStructureTraversal.h
x64-windows/include/vsg/raytracing/DescriptorAccelerationStructure.h
x64-windows/include/vsg/raytracing/RayTracingPipeline.h
x64-windows/include/vsg/raytracing/RayTracingShaderGroup.h
x64-windows/include/vsg/raytracing/TopLevelAccelerationStructure.h
x64-windows/include/vsg/raytracing/TraceRays.h
x64-windows/include/vsg/state/
x64-windows/include/vsg/state/ArrayState.h
x64-windows/include/vsg/state/BindDescriptorSet.h
x64-windows/include/vsg/state/Buffer.h
x64-windows/include/vsg/state/BufferInfo.h
x64-windows/include/vsg/state/BufferView.h
x64-windows/include/vsg/state/ColorBlendState.h
x64-windows/include/vsg/state/ComputePipeline.h
x64-windows/include/vsg/state/DepthStencilState.h
x64-windows/include/vsg/state/Descriptor.h
x64-windows/include/vsg/state/DescriptorBuffer.h
x64-windows/include/vsg/state/DescriptorImage.h
x64-windows/include/vsg/state/DescriptorSet.h
x64-windows/include/vsg/state/DescriptorSetLayout.h
x64-windows/include/vsg/state/DescriptorTexelBufferView.h
x64-windows/include/vsg/state/DynamicState.h
x64-windows/include/vsg/state/GraphicsPipeline.h
x64-windows/include/vsg/state/Image.h
x64-windows/include/vsg/state/ImageInfo.h
x64-windows/include/vsg/state/ImageView.h
x64-windows/include/vsg/state/InputAssemblyState.h
x64-windows/include/vsg/state/MultisampleState.h
x64-windows/include/vsg/state/PipelineLayout.h
x64-windows/include/vsg/state/PushConstants.h
x64-windows/include/vsg/state/QueryPool.h
x64-windows/include/vsg/state/RasterizationState.h
x64-windows/include/vsg/state/ResourceHints.h
x64-windows/include/vsg/state/Sampler.h
x64-windows/include/vsg/state/ShaderModule.h
x64-windows/include/vsg/state/ShaderStage.h
x64-windows/include/vsg/state/StateCommand.h
x64-windows/include/vsg/state/StateSwitch.h
x64-windows/include/vsg/state/TessellationState.h
x64-windows/include/vsg/state/VertexInputState.h
x64-windows/include/vsg/state/ViewDependentState.h
x64-windows/include/vsg/state/ViewportState.h
x64-windows/include/vsg/state/material.h
x64-windows/include/vsg/text/
x64-windows/include/vsg/text/CpuLayoutTechnique.h
x64-windows/include/vsg/text/Font.h
x64-windows/include/vsg/text/GlyphMetrics.h
x64-windows/include/vsg/text/GpuLayoutTechnique.h
x64-windows/include/vsg/text/StandardLayout.h
x64-windows/include/vsg/text/Text.h
x64-windows/include/vsg/text/TextGroup.h
x64-windows/include/vsg/text/TextLayout.h
x64-windows/include/vsg/text/TextTechnique.h
x64-windows/include/vsg/threading/
x64-windows/include/vsg/threading/ActivityStatus.h
x64-windows/include/vsg/threading/Affinity.h
x64-windows/include/vsg/threading/Barrier.h
x64-windows/include/vsg/threading/DeleteQueue.h
x64-windows/include/vsg/threading/FrameBlock.h
x64-windows/include/vsg/threading/Latch.h
x64-windows/include/vsg/threading/OperationQueue.h
x64-windows/include/vsg/threading/OperationThreads.h
x64-windows/include/vsg/threading/atomics.h
x64-windows/include/vsg/ui/
x64-windows/include/vsg/ui/ApplicationEvent.h
x64-windows/include/vsg/ui/CollectEvents.h
x64-windows/include/vsg/ui/FrameStamp.h
x64-windows/include/vsg/ui/KeyEvent.h
x64-windows/include/vsg/ui/Keyboard.h
x64-windows/include/vsg/ui/PlayEvents.h
x64-windows/include/vsg/ui/PointerEvent.h
x64-windows/include/vsg/ui/PrintEvents.h
x64-windows/include/vsg/ui/RecordEvents.h
x64-windows/include/vsg/ui/ScrollWheelEvent.h
x64-windows/include/vsg/ui/ShiftEventTime.h
x64-windows/include/vsg/ui/TouchEvent.h
x64-windows/include/vsg/ui/UIEvent.h
x64-windows/include/vsg/ui/WindowEvent.h
x64-windows/include/vsg/utils/
x64-windows/include/vsg/utils/Builder.h
x64-windows/include/vsg/utils/CommandLine.h
x64-windows/include/vsg/utils/ComputeBounds.h
x64-windows/include/vsg/utils/CoordinateSpace.h
x64-windows/include/vsg/utils/FindDynamicObjects.h
x64-windows/include/vsg/utils/GpuAnnotation.h
x64-windows/include/vsg/utils/GraphicsPipelineConfigurator.h
x64-windows/include/vsg/utils/Instrumentation.h
x64-windows/include/vsg/utils/Intersector.h
x64-windows/include/vsg/utils/LineSegmentIntersector.h
x64-windows/include/vsg/utils/LoadPagedLOD.h
x64-windows/include/vsg/utils/PolytopeIntersector.h
x64-windows/include/vsg/utils/PrimitiveFunctor.h
x64-windows/include/vsg/utils/Profiler.h
x64-windows/include/vsg/utils/PropagateDynamicObjects.h
x64-windows/include/vsg/utils/ShaderCompiler.h
x64-windows/include/vsg/utils/ShaderSet.h
x64-windows/include/vsg/utils/SharedObjects.h
x64-windows/include/vsg/utils/TracyInstrumentation.h
x64-windows/include/vsg/vk/
x64-windows/include/vsg/vk/AllocationCallbacks.h
x64-windows/include/vsg/vk/CommandBuffer.h
x64-windows/include/vsg/vk/CommandPool.h
x64-windows/include/vsg/vk/Context.h
x64-windows/include/vsg/vk/DescriptorPool.h
x64-windows/include/vsg/vk/DescriptorPools.h
x64-windows/include/vsg/vk/Device.h
x64-windows/include/vsg/vk/DeviceExtensions.h
x64-windows/include/vsg/vk/DeviceFeatures.h
x64-windows/include/vsg/vk/DeviceMemory.h
x64-windows/include/vsg/vk/Fence.h
x64-windows/include/vsg/vk/Framebuffer.h
x64-windows/include/vsg/vk/Instance.h
x64-windows/include/vsg/vk/InstanceExtensions.h
x64-windows/include/vsg/vk/MemoryBufferPools.h
x64-windows/include/vsg/vk/PhysicalDevice.h
x64-windows/include/vsg/vk/Queue.h
x64-windows/include/vsg/vk/RenderPass.h
x64-windows/include/vsg/vk/ResourceRequirements.h
x64-windows/include/vsg/vk/Semaphore.h
x64-windows/include/vsg/vk/State.h
x64-windows/include/vsg/vk/SubmitCommands.h
x64-windows/include/vsg/vk/Surface.h
x64-windows/include/vsg/vk/Swapchain.h
x64-windows/include/vsg/vk/vk_buffer.h
x64-windows/include/vsg/vk/vulkan.h
x64-windows/lib/
x64-windows/lib/vsg.lib
x64-windows/share/
x64-windows/share/vsg/
x64-windows/share/vsg/FindVulkan.cmake
x64-windows/share/vsg/copyright
x64-windows/share/vsg/uninstall.cmake
x64-windows/share/vsg/vcpkg.spdx.json
x64-windows/share/vsg/vcpkg_abi_info.txt
x64-windows/share/vsg/vsgConfig.cmake
x64-windows/share/vsg/vsgConfigVersion.cmake
x64-windows/share/vsg/vsgMacros.cmake
x64-windows/share/vsg/vsgTargets-debug.cmake
x64-windows/share/vsg/vsgTargets-release.cmake
x64-windows/share/vsg/vsgTargets.cmake
