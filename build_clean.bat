@echo off
echo 正在清理Rocky项目编译目录...
echo.

:: 检查编译目录是否存在
if not exist "F:/rockyb2" (
    echo 编译目录 F:/rockyb2 不存在，无需清理
    pause
    exit /b 0
)

echo 警告：将删除编译目录 F:/rockyb2 下的所有文件
echo 确定要继续吗？
echo 按任意键继续，或按 Ctrl+C 取消...
pause > nul

:: 删除编译目录
echo 正在删除 F:/rockyb2 目录...
rmdir /s /q "F:/rockyb2"

if %errorlevel% neq 0 (
    echo 清理失败！可能有文件正在使用中
    pause
    exit /b 1
)

echo.
echo 清理完成！
echo 编译目录 F:/rockyb2 已删除
echo.
pause 