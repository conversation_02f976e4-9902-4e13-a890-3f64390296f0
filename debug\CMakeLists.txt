cmake_minimum_required(VERSION 3.20)
project(debug_mercator)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

if(MSVC)
    add_compile_options(/utf-8)
endif()

set(ROCKY_INCLUDE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/include")
set(ROCKY_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/lib")
set(ROCKY_BIN_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/bin")

add_executable(debug_mercator debug_mercator.cpp)
target_include_directories(debug_mercator PRIVATE ${ROCKY_INCLUDE_DIR})
target_link_libraries(debug_mercator PRIVATE "${ROCKY_LIB_DIR}/rocky.lib")

if(WIN32)
    add_custom_command(TARGET debug_mercator POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${ROCKY_BIN_DIR}/rocky.dll"
        $<TARGET_FILE_DIR:debug_mercator>)
        
    add_custom_command(TARGET debug_mercator POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "C:/dev/vcpkg/installed/x64-windows/bin/GeographicLib.dll"
        $<TARGET_FILE_DIR:debug_mercator>)
endif()
