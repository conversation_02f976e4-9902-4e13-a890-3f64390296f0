cmake_minimum_required(VERSION 3.10.0)

@PACKA<PERSON>_INIT@

set(ROCKY_VERSION @PROJECT_VERSION@)

if(NOT CMAKE_CXX_STANDARD)
    set(CMAKE_CXX_STANDARD 17)
    set(CMAKE_CXX_STANDARD_REQUIRED ON)
    set(CMAKE_CXX_EXTENSIONS OFF)
endif()

set(ROCKY_INCLUDE_DIR "${PACKAGE_PREFIX_DIR}/include")
set(ROCKY_SHARE_DIR "${PACKAGE_PREFIX_DIR}/share/rocky")
set_and_check(ROCKY_BUILD_DIR "${PACKAGE_PREFIX_DIR}")

include(CMakeFindDependencyMacro)

# Find public dependencies
find_dependency(glm CONFIG)
find_dependency(spdlog CONFIG)
find_dependency(EnTT CONFIG)
find_dependency(vsg CONFIG)

# Find private dependencies, for linking to static library
if(NOT @ROCKY_BUILD_SHARED_LIBS@)
    find_dependency(PROJ CONFIG)
    if(@ROCKY_HAS_VSGXCHANGE@)
        find_dependency(vsgXchange CONFIG)
    endif()
    if(@ROCKY_HAS_GDAL@)
        find_dependency(GDAL CONFIG)
    endif()
    if(@ROCKY_HAS_JSON@)
        find_dependency(nlohmann_json CONFIG)
    endif()
endif()

if(NOT TARGET "rocky")
  include("${CMAKE_CURRENT_LIST_DIR}/rocky-targets.cmake")
endif()

set(ROCKY_FOUND TRUE)
