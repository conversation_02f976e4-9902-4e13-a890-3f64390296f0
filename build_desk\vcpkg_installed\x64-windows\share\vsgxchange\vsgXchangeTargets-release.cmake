#----------------------------------------------------------------
# Generated CMake target import file for configuration "Release".
#----------------------------------------------------------------

# Commands may need to know the format version.
set(CMAKE_IMPORT_FILE_VERSION 1)

# Import target "vsgXchange::vsgXchange" for configuration "Release"
set_property(TARGET vsgXchange::vsgXchange APPEND PROPERTY IMPORTED_CONFIGURATIONS RELEASE)
set_target_properties(vsgXchange::vsgXchange PROPERTIES
  IMPORTED_IMPLIB_RELEASE "${_IMPORT_PREFIX}/lib/vsgXchange.lib"
  IMPORTED_LINK_DEPENDENT_LIBRARIES_RELEASE "assimp::assimp"
  IMPORTED_LOCATION_RELEASE "${_IMPORT_PREFIX}/bin/vsgXchange.dll"
  )

list(APPEND _cmake_import_check_targets vsgXchange::vsgXchange )
list(APPEND _cmake_import_check_files_for_vsgXchange::vsgXchange "${_IMPORT_PREFIX}/lib/vsgXchange.lib" "${_IMPORT_PREFIX}/bin/vsgXchange.dll" )

# Commands beyond this point should not need to know the version.
set(CMAKE_IMPORT_FILE_VERSION)
