/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#pragma once

#include <rocky/Common.h>
#include <rocky/Image.h>
#include <rocky/Status.h>
#include <memory>
#include <istream>

namespace ROCKY_NAMESPACE
{
    namespace TinyTIFFSupport
    {
        /**
         * TinyTIFF-based TIFF reader for Rocky
         * Lightweight alternative to GDAL for basic TIFF reading
         */
        class ROCKY_EXPORT TinyTIFFReader
        {
        public:
            /**
             * Read a TIFF image from a memory buffer
             * @param data Raw TIFF data
             * @param length Size of data in bytes
             * @return Image object or error status
             */
            static Result<std::shared_ptr<Image>> readFromMemory(
                const unsigned char* data, 
                size_t length);

            /**
             * Read a TIFF image from a stream
             * @param stream Input stream containing TIFF data
             * @return Image object or error status
             */
            static Result<std::shared_ptr<Image>> readFromStream(
                std::istream& stream);

            /**
             * Check if the data appears to be a valid TIFF file
             * @param data First few bytes of the file
             * @param length Number of bytes to check
             * @return true if appears to be TIFF format
             */
            static bool isTIFFFormat(const unsigned char* data, size_t length);

        private:
            // Helper methods for different TIFF data types
            static Result<std::shared_ptr<Image>> readFloat32TIFF(
                const unsigned char* data, size_t length);
            
            static Result<std::shared_ptr<Image>> readUInt16TIFF(
                const unsigned char* data, size_t length);
                
            static Result<std::shared_ptr<Image>> readUInt8TIFF(
                const unsigned char* data, size_t length);
        };

        /**
         * VSG ReaderWriter implementation using TinyTIFF
         */
        class ROCKY_EXPORT TinyTIFF_VSG_ReaderWriter
        {
        public:
            TinyTIFF_VSG_ReaderWriter();
            
            bool canRead(const std::string& extension) const;
            
            Result<std::shared_ptr<Image>> read(
                std::istream& in, 
                const std::string& extension) const;
        };
    }
}
