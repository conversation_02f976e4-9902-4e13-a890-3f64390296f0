name: Doxygen GitHub Pages Deploy Action

on:
  push:
  workflow_dispatch:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout repository
      uses: actions/checkout@v3
      with:
        submodules: "false"

    - name: Install Doxygen
      run: sudo apt-get install doxygen graphviz -y
      shell: bash

    - name: Generate Doxygen Documentation
      run: doxygen Doxyfile
      shell: bash
      
    - name: Create .nojekyll (ensures pages with underscores work on gh pages)
      run: touch docs/html/.nojekyll
      shell: bash

    - name: Deploy to GitHub Pages
      uses: JamesIves/github-pages-deploy-action@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        branch: gh-pages
        folder: docs/html
        target-folder: doxygen
