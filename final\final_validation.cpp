#include <rocky/SRS.h>
#include <rocky/GeoPoint.h>
#include <iostream>
#include <iomanip>
#include <cmath>

using namespace rocky;

int main() {
    std::cout << "Rocky SRS 重构最终验证" << std::endl;
    std::cout << "验证从PROJ库到GeographicLib的重构正确性" << std::endl;
    
    int passedTests = 0;
    int totalTests = 0;
    
    // 测试1: 基本坐标系创建
    std::cout << "\n=== 测试1: 基本坐标系创建 ===" << std::endl;
    totalTests++;
    
    SRS wgs84("epsg:4326");
    SRS webMercator("epsg:3857");
    SRS utm32N("epsg:32632");
    SRS invalidSRS("gibberish");
    
    bool test1_passed = true;
    
    if (!wgs84.valid()) {
        std::cout << "❌ WGS84创建失败" << std::endl;
        test1_passed = false;
    }
    if (!webMercator.valid()) {
        std::cout << "❌ Web Mercator创建失败" << std::endl;
        test1_passed = false;
    }
    if (!utm32N.valid()) {
        std::cout << "❌ UTM 32N创建失败" << std::endl;
        test1_passed = false;
    }
    if (invalidSRS.valid()) {
        std::cout << "❌ 无效SRS应该返回false" << std::endl;
        test1_passed = false;
    }
    
    if (test1_passed) {
        std::cout << "✅ 基本坐标系创建测试通过" << std::endl;
        passedTests++;
    }
    
    // 测试2: 坐标系属性
    std::cout << "\n=== 测试2: 坐标系属性 ===" << std::endl;
    totalTests++;
    
    bool test2_passed = true;
    
    if (!wgs84.isGeodetic() || wgs84.isProjected()) {
        std::cout << "❌ WGS84属性错误" << std::endl;
        test2_passed = false;
    }
    if (webMercator.isGeodetic() || !webMercator.isProjected()) {
        std::cout << "❌ Web Mercator属性错误" << std::endl;
        test2_passed = false;
    }
    if (utm32N.isGeodetic() || !utm32N.isProjected()) {
        std::cout << "❌ UTM 32N属性错误" << std::endl;
        test2_passed = false;
    }
    
    if (test2_passed) {
        std::cout << "✅ 坐标系属性测试通过" << std::endl;
        passedTests++;
    }
    
    // 测试3: Web Mercator关键边界转换
    std::cout << "\n=== 测试3: Web Mercator关键边界转换 ===" << std::endl;
    totalTests++;
    
    bool test3_passed = true;
    std::cout << std::fixed << std::setprecision(9);
    
    // 测试-180度边界
    GeoPoint west180(wgs84, -180.0, 0.0, 0.0);
    GeoPoint mercWest = west180.transform(webMercator);
    
    double expectedWestX = -20037508.342789244;
    double westError = std::abs(mercWest.x - expectedWestX);
    
    std::cout << "(-180°, 0°) -> (" << mercWest.x << ", " << mercWest.y << ")" << std::endl;
    std::cout << "期望X: " << expectedWestX << ", 误差: " << westError << " 米" << std::endl;
    
    if (westError > 1.0) {
        std::cout << "❌ 西边界转换误差过大" << std::endl;
        test3_passed = false;
    }
    
    // 测试反向转换
    GeoPoint mercBoundary(webMercator, -20037508.34278925, 0.0, 0.0);
    GeoPoint wgsBoundary = mercBoundary.transform(wgs84);
    
    double lonError = std::abs(wgsBoundary.x - (-180.0));
    std::cout << "(-20037508.34278925, 0) -> (" << wgsBoundary.x << ", " << wgsBoundary.y << ")" << std::endl;
    std::cout << "期望: (-180°, 0°), 经度误差: " << std::scientific << lonError << "°" << std::endl;
    
    if (lonError > 1e-6) {
        std::cout << "❌ 反向转换误差过大" << std::endl;
        test3_passed = false;
    }
    
    if (test3_passed) {
        std::cout << "✅ Web Mercator边界转换测试通过" << std::endl;
        passedTests++;
    }
    
    // 测试4: 往返转换精度
    std::cout << "\n=== 测试4: 往返转换精度 ===" << std::endl;
    totalTests++;
    
    bool test4_passed = true;
    
    // 测试北京坐标往返转换
    GeoPoint beijing(wgs84, 116.4074, 39.9042, 0.0);
    GeoPoint beijingMerc = beijing.transform(webMercator);
    GeoPoint beijingBack = beijingMerc.transform(wgs84);
    
    double bjLonError = std::abs(beijingBack.x - 116.4074);
    double bjLatError = std::abs(beijingBack.y - 39.9042);
    
    std::cout << std::fixed << std::setprecision(6);
    std::cout << "北京往返转换: (" << beijing.x << ", " << beijing.y << ") -> ";
    std::cout << "(" << beijingMerc.x << ", " << beijingMerc.y << ") -> ";
    std::cout << "(" << beijingBack.x << ", " << beijingBack.y << ")" << std::endl;
    
    std::cout << std::scientific << std::setprecision(2);
    std::cout << "往返误差: 经度=" << bjLonError << "°, 纬度=" << bjLatError << "°" << std::endl;
    
    if (bjLonError > 1e-9 || bjLatError > 1e-9) {
        std::cout << "❌ 往返转换精度不足" << std::endl;
        test4_passed = false;
    }
    
    if (test4_passed) {
        std::cout << "✅ 往返转换精度测试通过" << std::endl;
        passedTests++;
    }
    
    // 测试5: 椭球体参数
    std::cout << "\n=== 测试5: 椭球体参数 ===" << std::endl;
    totalTests++;
    
    bool test5_passed = true;
    
    const Ellipsoid& ellipsoid = wgs84.ellipsoid();
    double semiMajor = ellipsoid.semiMajorAxis();
    double semiMinor = ellipsoid.semiMinorAxis();
    
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "WGS84椭球体参数:" << std::endl;
    std::cout << "  长半轴: " << semiMajor << " 米" << std::endl;
    std::cout << "  短半轴: " << semiMinor << " 米" << std::endl;
    
    if (std::abs(semiMajor - 6378137.0) > 1.0) {
        std::cout << "❌ 椭球体长半轴错误" << std::endl;
        test5_passed = false;
    }
    
    if (test5_passed) {
        std::cout << "✅ 椭球体参数测试通过" << std::endl;
        passedTests++;
    }
    
    // 总结
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "最终验证结果: " << passedTests << "/" << totalTests << " 测试通过" << std::endl;
    
    if (passedTests == totalTests) {
        std::cout << "🎉 所有关键测试通过！" << std::endl;
        std::cout << "✅ Rocky SRS重构成功完成" << std::endl;
        std::cout << "✅ GeographicLib适配器工作正常" << std::endl;
        std::cout << "✅ 图层加载所需的坐标转换功能验证通过" << std::endl;
        std::cout << "✅ 可以安全地用于生产环境" << std::endl;
        return 0;
    } else {
        std::cout << "⚠️  仍有 " << (totalTests - passedTests) << " 个测试失败" << std::endl;
        std::cout << "需要进一步调试和修复" << std::endl;
        return 1;
    }
}
