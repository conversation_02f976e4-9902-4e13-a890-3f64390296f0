assimp 9025f0be1367779b748de6a6908e3d83c019994c14184fd00fb6a114a517b771
cmake 3.30.1
features assimp;core;freetype
freetype d567993976f4cb44c08b2e5a53c829754d97209871617290641b45af1aafaade
portfile.cmake c12731044e8c26b4f8717d8c0758d4ad313bc2e354c9207c41b63e9f19e3ae86
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg-cmake fa0a421976433b6b0635f7ccfd6e08f02a20aef877e749d6792bc1d9ae8233af
vcpkg-cmake-config 8d261494f5cab6cecd7851edc7eb14f27f3c82cd259e8a56095998d1199973a0
vcpkg.json 74ec46685ed9d84322265596739cd803aabd2f7a9a3fddefd0fca79e09c299ac
vcpkg_check_features 943b217e0968d64cf2cb9c272608e6a0b497377e792034f819809a79e1502c2b
vcpkg_clean_executables_in_bin 5f7427ddf120583cf1f429861989e937858ee7a8c4deb419dca6f3f2fee86a65
vcpkg_copy_pdbs d15c2f2822d93ecb9023af44803328e491c5374136e0813600289582a72d196d
vcpkg_copy_tools 3d45ff761bddbabe8923b52330168dc3abd295fa469d3f2e47cb14dce85332d5
vcpkg_from_git 96ed81968f76354c00096dd8cd4e63c6a235fa969334a11ab18d11c0c512ff58
vcpkg_from_github 1284881728e98a182fc63e841be04e39b8c94753fdc361603c72a63c1adcf846
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vsg 75ed1d1386640fde06416b48f8e974d469ae80b9e1944318026979955a2f483c
