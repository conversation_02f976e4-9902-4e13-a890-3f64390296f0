cmake_minimum_required(VERSION 3.20)
project(test_srs_refactor)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译器选项
if(MSVC)
    add_compile_options(/utf-8)
endif()

# 直接使用头文件和库文件
set(ROCKY_INCLUDE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/include")
set(ROCKY_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/lib")
set(ROCKY_BIN_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/bin")

# 创建测试可执行文件
add_executable(test_srs_refactor test_srs_refactor.cpp)

# 设置包含目录
target_include_directories(test_srs_refactor PRIVATE ${ROCKY_INCLUDE_DIR})

# 链接Rocky库
target_link_libraries(test_srs_refactor PRIVATE "${ROCKY_LIB_DIR}/rocky.lib")

# 设置运行时库路径
if(WIN32)
    # 复制DLL到输出目录
    add_custom_command(TARGET test_srs_refactor POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${ROCKY_BIN_DIR}/rocky.dll"
        $<TARGET_FILE_DIR:test_srs_refactor>)
endif()
