/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#pragma once

#include <rocky/Common.h>
#include <rocky/json.h>
#include <string>
#include <optional>
#include <filesystem>

namespace ROCKY_NAMESPACE
{
    /**
     * 配置管理器 - 负责读取和管理应用程序配置
     */
    class ROCKY_EXPORT Config
    {
    public:
        Config() = default;
        
        //! 从文件加载配置
        bool loadFromFile(const std::string& filename);
        
        //! 从JSON字符串加载配置
        bool loadFromJSON(const std::string& jsonStr);
        
        //! 获取代理设置
        std::optional<ProxySettings> getProxySettings() const;
        
        //! 设置代理设置
        void setProxySettings(const ProxySettings& proxy);
        
        //! 保存配置到文件
        bool saveToFile(const std::string& filename) const;
        
        //! 获取配置的JSON表示
        std::string toJSON() const;
        
        //! 创建默认配置文件
        static bool createDefaultConfig(const std::string& filename);
        
    private:
        JSON _json;
        std::optional<ProxySettings> _proxySettings;
        
        void parseProxySettings();
    };
    
    /**
     * 全局配置管理器
     */
    class ROCKY_EXPORT GlobalConfig
    {
    public:
        //! 获取全局配置实例
        static GlobalConfig& instance();
        
        //! 初始化配置（从默认位置加载）
        bool initialize();
        
        //! 初始化配置（从指定文件加载）
        bool initialize(const std::string& configFile);
        
        //! 获取代理设置
        std::optional<ProxySettings> getProxySettings() const;
        
        //! 获取配置文件路径
        std::string getConfigFilePath() const;
        
    private:
        GlobalConfig() = default;
        Config _config;
        std::string _configFilePath;
        bool _initialized = false;
    };
}
