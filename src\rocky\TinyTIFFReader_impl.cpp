/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#include "TinyTIFFReader.h"
#include "Log.h"

// Include TinyTIFF headers
extern "C"
{
#include "tinytiffreader.h"
}

#include <sstream>
#include <vector>
#include <cstring>
#include <fstream>
#include <cstdio>

using namespace ROCKY_NAMESPACE;
using namespace ROCKY_NAMESPACE::TinyTIFFSupport;

#undef LC
#define LC "[TinyTIFF] "

bool TinyTIFFReader::isTIFFFormat(const unsigned char* data, size_t length)
{
    if (length < 4)
        return false;
        
    // Check TIFF magic numbers
    // Little-endian: 49 49 2A 00 (II*)
    // Big-endian: 4D 4D 00 2A (MM*)
    return (data[0] == 0x49 && data[1] == 0x49 && data[2] == 0x2A && data[3] == 0x00) ||
           (data[0] == 0x4D && data[1] == 0x4D && data[2] == 0x00 && data[3] == 0x2A);
}

Result<std::shared_ptr<Image>> TinyTIFFReader::readFromMemory(
    const unsigned char* data, size_t length)
{
    if (!data || length == 0)
        return Status(Status::ConfigurationError, LC "Invalid input data");
        
    if (!isTIFFFormat(data, length))
        return Status(Status::ConfigurationError, LC "Not a valid TIFF format");
    
    // TinyTIFF only supports file-based reading, so we need to create a temporary file
    std::string tempFilename = std::tmpnam(nullptr);
    if (tempFilename.empty())
        tempFilename = "rocky_temp_tiff";
    tempFilename += ".tif";
    
    // Write data to temporary file
    std::ofstream tempFile(tempFilename, std::ios::binary);
    if (!tempFile.is_open())
    {
        return Status(Status::ResourceUnavailable, LC "Failed to open temporary file for writing");
    }
    
    tempFile.write(reinterpret_cast<const char*>(data), length);
    tempFile.close();
    
    // Open TIFF using TinyTIFF
    TinyTIFFReaderFile* tiff = TinyTIFFReader_open(tempFilename.c_str());
        
    if (!tiff)
    {
        // Clean up temporary file
        std::remove(tempFilename.c_str());
        return Status(Status::ResourceUnavailable, LC "Failed to open TIFF file");
    }
    
    // Get image dimensions and properties
    uint32_t width = TinyTIFFReader_getWidth(tiff);
    uint32_t height = TinyTIFFReader_getHeight(tiff);
    uint16_t bitsPerSample = TinyTIFFReader_getBitsPerSample(tiff, 0);
    uint16_t samplesPerPixel = TinyTIFFReader_getSamplesPerPixel(tiff);
    
    Log()->info(LC "TIFF dimensions: {}x{}, {} bits/sample, {} samples/pixel", 
                width, height, bitsPerSample, samplesPerPixel);
    
    std::shared_ptr<Image> image;
    
    // Handle different data types - focus on elevation data (32-bit float)
    if (bitsPerSample == 32 && samplesPerPixel == 1)
    {
        // 32-bit float elevation data
        image = Image::create(Image::R32_SFLOAT, width, height);
        if (image)
        {
            float* pixels = image->data<float>();
            if (!TinyTIFFReader_getSampleData(tiff, pixels, 0))
            {
                TinyTIFFReader_close(tiff);
                std::remove(tempFilename.c_str());
                return Status(Status::ResourceUnavailable, LC "Failed to read 32-bit float data");
            }
        }
    }
    else if (bitsPerSample == 16 && samplesPerPixel == 1)
    {
        // 16-bit integer elevation data
        image = Image::create(Image::R16_UNORM, width, height);
        if (image)
        {
            uint16_t* pixels = image->data<uint16_t>();
            if (!TinyTIFFReader_getSampleData(tiff, pixels, 0))
            {
                TinyTIFFReader_close(tiff);
                std::remove(tempFilename.c_str());
                return Status(Status::ResourceUnavailable, LC "Failed to read 16-bit data");
            }
        }
    }
    else
    {
        TinyTIFFReader_close(tiff);
        std::remove(tempFilename.c_str());
        return Status(Status::ConfigurationError, 
                     LC "Unsupported TIFF format for elevation data: " + std::to_string(bitsPerSample) + 
                     " bits/sample, " + std::to_string(samplesPerPixel) + " samples/pixel");
    }
    
    TinyTIFFReader_close(tiff);
    std::remove(tempFilename.c_str());
    
    if (!image)
        return Status(Status::ResourceUnavailable, LC "Failed to create image");
        
    return image;
}

Result<std::shared_ptr<Image>> TinyTIFFReader::readFromStream(std::istream& stream)
{
    // Read entire stream into memory
    std::ostringstream buffer;
    buffer << stream.rdbuf();
    std::string data = buffer.str();
    
    if (data.empty())
        return Status(Status::ConfigurationError, LC "Empty input stream");
        
    return readFromMemory(
        reinterpret_cast<const unsigned char*>(data.c_str()), 
        data.size());
}

// VSG ReaderWriter implementation
TinyTIFF_VSG_ReaderWriter::TinyTIFF_VSG_ReaderWriter()
{
}

bool TinyTIFF_VSG_ReaderWriter::canRead(const std::string& extension) const
{
    return extension == ".tif" || extension == ".tiff";
}

Result<std::shared_ptr<Image>> TinyTIFF_VSG_ReaderWriter::read(
    std::istream& in, const std::string& extension) const
{
    if (!canRead(extension))
        return Status(Status::ConfigurationError, LC "Unsupported extension: " + extension);
        
    return TinyTIFFReader::readFromStream(in);
}
