#!/usr/bin/env python3
"""
验证ReadyMap高程计算公式的正确性
通过分析已知地理位置的高程数据来验证计算公式
"""

import struct
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

def read_tiff_elevation_data(filepath):
    """读取TIFF文件中的高程数据"""
    with open(filepath, 'rb') as f:
        data = f.read()
    
    # 简单的TIFF解析 - 假设数据从某个偏移开始
    # 这是一个简化版本，实际的TIFF解析更复杂
    
    # 检查文件头
    if data[0:2] == b'II':
        endian = '<'  # Little endian
    elif data[0:2] == b'MM':
        endian = '>'  # Big endian
    else:
        raise ValueError("无效的TIFF文件")
    
    # 对于ReadyMap的TIFF，数据通常从某个固定偏移开始
    # 我们需要找到实际的图像数据位置
    
    # 简单方法：假设数据在文件末尾的257*257*2字节
    expected_data_size = 257 * 257 * 2
    data_start = len(data) - expected_data_size
    
    if data_start < 0:
        # 如果文件太小，尝试从头开始读取
        data_start = max(0, len(data) - expected_data_size)
    
    # 读取16位无符号整数数据
    raw_data = data[data_start:data_start + expected_data_size]
    
    if len(raw_data) < expected_data_size:
        print(f"警告: 数据大小不足 {len(raw_data)} < {expected_data_size}")
        # 填充不足的部分
        raw_data += b'\x00' * (expected_data_size - len(raw_data))
    
    # 解析为16位无符号整数数组
    elevation_raw = struct.unpack(endian + 'H' * (len(raw_data) // 2), raw_data)
    
    # 转换为numpy数组并重塑为257x257
    elevation_array = np.array(elevation_raw, dtype=np.uint16)
    
    if len(elevation_array) >= 257 * 257:
        elevation_array = elevation_array[:257*257].reshape(257, 257)
    else:
        # 如果数据不足，创建一个较小的数组
        size = int(np.sqrt(len(elevation_array)))
        elevation_array = elevation_array[:size*size].reshape(size, size)
    
    return elevation_array

def analyze_elevation_values(raw_values, tile_info):
    """分析高程值的分布和合理性"""
    print(f"\n=== 高程数据分析: {tile_info} ===")
    
    # 基本统计
    print(f"原始数据范围: {raw_values.min()} - {raw_values.max()}")
    print(f"数据形状: {raw_values.shape}")
    
    # 应用ReadyMap的转换公式: elevation = raw_value - 32768
    elevation_meters = raw_values.astype(np.int32) - 32768
    
    # 过滤NoData值 (0 和 65535 对应 -32768 和 32767)
    valid_mask = (raw_values != 0) & (raw_values != 65535)
    valid_elevations = elevation_meters[valid_mask]
    
    if len(valid_elevations) == 0:
        print("❌ 没有有效的高程数据")
        return False
    
    print(f"有效像素: {len(valid_elevations)}/{raw_values.size} ({len(valid_elevations)/raw_values.size*100:.1f}%)")
    print(f"高程范围: {valid_elevations.min()} - {valid_elevations.max()} 米")
    print(f"平均高程: {valid_elevations.mean():.1f} 米")
    print(f"标准差: {valid_elevations.std():.1f} 米")
    
    # 检查高程值的合理性
    issues = []
    
    if valid_elevations.min() < -11000:
        issues.append(f"最低高程异常: {valid_elevations.min()} 米")
    
    if valid_elevations.max() > 9000:
        issues.append(f"最高高程异常: {valid_elevations.max()} 米")
    
    # 检查数据的变化性
    elevation_std = valid_elevations.std()
    if elevation_std < 1.0:
        issues.append(f"高程变化过小: 标准差 {elevation_std:.2f} 米")
    
    if issues:
        print("⚠️ 发现问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ 高程数据合理")
        return True

def create_elevation_visualization(raw_values, tile_info, output_dir):
    """创建高程数据可视化"""
    try:
        import matplotlib.pyplot as plt
        
        # 转换为高程值
        elevation_meters = raw_values.astype(np.int32) - 32768
        
        # 过滤NoData值
        valid_mask = (raw_values != 0) & (raw_values != 65535)
        elevation_display = elevation_meters.copy().astype(np.float32)
        elevation_display[~valid_mask] = np.nan
        
        # 创建图像
        plt.figure(figsize=(10, 8))
        
        # 高程图
        plt.subplot(2, 2, 1)
        im1 = plt.imshow(elevation_display, cmap='terrain', interpolation='nearest')
        plt.colorbar(im1, label='高程 (米)')
        plt.title(f'高程图 - {tile_info}')
        
        # 原始数据直方图
        plt.subplot(2, 2, 2)
        valid_elevations = elevation_meters[valid_mask]
        plt.hist(valid_elevations, bins=50, alpha=0.7, edgecolor='black')
        plt.xlabel('高程 (米)')
        plt.ylabel('像素数量')
        plt.title('高程分布直方图')
        
        # 3D表面图 (简化版)
        plt.subplot(2, 2, 3)
        # 降采样以提高性能
        step = max(1, raw_values.shape[0] // 50)
        elevation_sample = elevation_display[::step, ::step]
        plt.imshow(elevation_sample, cmap='terrain', interpolation='bilinear')
        plt.title('高程表面 (降采样)')
        
        # 统计信息
        plt.subplot(2, 2, 4)
        plt.axis('off')
        stats_text = f"""统计信息:
        
有效像素: {len(valid_elevations)}/{raw_values.size}
有效率: {len(valid_elevations)/raw_values.size*100:.1f}%

高程范围: {valid_elevations.min():.0f} - {valid_elevations.max():.0f} 米
平均高程: {valid_elevations.mean():.1f} 米
标准差: {valid_elevations.std():.1f} 米

原始数据范围: {raw_values.min()} - {raw_values.max()}
转换公式: elevation = raw_value - 32768
        """
        plt.text(0.1, 0.9, stats_text, transform=plt.gca().transAxes, 
                verticalalignment='top', fontfamily='monospace', fontsize=9)
        
        plt.tight_layout()
        
        # 保存图像
        output_file = output_dir / f"elevation_analysis_{tile_info.replace(' ', '_').replace(',', '')}.png"
        plt.savefig(output_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"可视化图像已保存: {output_file}")
        return True
        
    except ImportError:
        print("matplotlib未安装，跳过可视化")
        return False
    except Exception as e:
        print(f"可视化创建失败: {e}")
        return False

def verify_elevation_calculation():
    """验证高程计算公式的正确性"""
    
    print("=== ReadyMap 高程计算公式验证 ===\n")
    
    cache_dir = Path("cache/elevation")
    analysis_dir = Path("cache/analysis")
    analysis_dir.mkdir(exist_ok=True)
    
    if not cache_dir.exists():
        print("❌ 缓存目录不存在，请先运行 validate_readymap_elevation.py")
        return
    
    tiff_files = list(cache_dir.glob("*.tif"))
    if not tiff_files:
        print("❌ 没有找到TIFF文件，请先运行 validate_readymap_elevation.py")
        return
    
    print(f"找到 {len(tiff_files)} 个TIFF文件进行分析\n")
    
    successful_analyses = 0
    
    for tiff_file in tiff_files:
        try:
            print(f"分析文件: {tiff_file.name}")
            
            # 读取高程数据
            raw_elevation = read_tiff_elevation_data(tiff_file)
            
            # 分析高程值
            tile_info = tiff_file.stem.replace('elevation_', '').replace('_', ', ')
            is_valid = analyze_elevation_values(raw_elevation, tile_info)
            
            if is_valid:
                successful_analyses += 1
                
                # 创建可视化
                create_elevation_visualization(raw_elevation, tile_info, analysis_dir)
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
    
    # 生成总结报告
    print(f"\n{'='*50}")
    print(f"验证完成: {successful_analyses}/{len(tiff_files)} 个文件分析成功")
    
    if successful_analyses == len(tiff_files):
        print("✅ 所有高程数据验证通过！")
        print("✅ ReadyMap高程计算公式正确: elevation = (raw_value - 32768) 米")
    else:
        print("⚠️ 部分文件分析失败，请检查数据质量")
    
    # 保存验证结果
    report_file = analysis_dir / "elevation_calculation_verification.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== ReadyMap 高程计算公式验证报告 ===\n\n")
        f.write(f"验证文件数: {len(tiff_files)}\n")
        f.write(f"成功分析: {successful_analyses}\n")
        f.write(f"成功率: {successful_analyses/len(tiff_files)*100:.1f}%\n\n")
        
        f.write("验证结论:\n")
        if successful_analyses == len(tiff_files):
            f.write("✅ ReadyMap高程计算公式验证通过\n")
            f.write("✅ 正确的转换公式: elevation_meters = raw_uint16_value - 32768\n")
            f.write("✅ NoData值: 0 和 65535 (对应 -32768 和 32767 米)\n")
            f.write("✅ 有效高程范围: -32768 到 +32767 米\n")
        else:
            f.write("⚠️ 验证过程中发现问题，需要进一步检查\n")
        
        f.write("\n技术细节:\n")
        f.write("- 数据格式: TIFF 16位无符号整数\n")
        f.write("- 瓦片尺寸: 257x257 像素\n")
        f.write("- 字节序: Little Endian\n")
        f.write("- 坐标系: Web Mercator (EPSG:3857)\n")
    
    print(f"\n详细验证报告已保存: {report_file}")

if __name__ == "__main__":
    verify_elevation_calculation()
