﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\d81dafa326958baee5cbf26cbf81d02a\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/rocky.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSSL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgMacros.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\rocky\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\install-export-files.cmake;F:\cmo-dev\my_osgearth_web\rocky\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_engine\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Version.h.in;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rocky-config.cmake.in;F:\cmo-dev\my_osgearth_web\rocky\src\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_engine\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/rocky.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSSL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgMacros.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\rocky\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\install-export-files.cmake;F:\cmo-dev\my_osgearth_web\rocky\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_engine\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Version.h.in;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rocky-config.cmake.in;F:\cmo-dev\my_osgearth_web\rocky\src\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_engine\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/rocky.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSSL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgMacros.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\rocky\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\install-export-files.cmake;F:\cmo-dev\my_osgearth_web\rocky\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_engine\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Version.h.in;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rocky-config.cmake.in;F:\cmo-dev\my_osgearth_web\rocky\src\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_engine\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/rocky.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSSL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geographiclib\geographiclib-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\geos\geos-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgMacros.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets.cmake;C:\dev\vcpkg\scripts\buildsystems\vcpkg.cmake;F:\cmo-dev\my_osgearth_web\rocky\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeCCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeCXXCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeRCCompiler.cmake;F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\3.26.4\CMakeSystem.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\install-export-files.cmake;F:\cmo-dev\my_osgearth_web\rocky\src\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_demo\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_engine\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\apps\rocky_simple\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\CMakeLists.txt;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Version.h.in;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rocky-config.cmake.in;F:\cmo-dev\my_osgearth_web\rocky\src\tests\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_simple\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_engine\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\CMakeFiles\generate.stamp;F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>