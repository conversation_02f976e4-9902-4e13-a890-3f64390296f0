@echo off
setlocal enabledelayedexpansion

echo 编译Rocky项目...
set BUILD_DIR=F:/rockyb2
set SRC_DIR=%~dp0

if not exist "%BUILD_DIR%" (
    echo 错误: 编译目录 %BUILD_DIR% 不存在.
    echo 请先运行 build_config_core.bat.
    pause
    exit /b 1
)

echo 编译目录: %BUILD_DIR%
echo.

echo 开始编译 Release 版本...
cmake --build "%BUILD_DIR%" --config Release -j%NUMBER_OF_PROCESSORS%
if !errorlevel! neq 0 (
    echo.
    echo 编译失败!
    pause
    exit /b 1
)
echo 编译成功.
echo.

echo 复制文件到发布目录...
set REDIST_DIR=%SRC_DIR%redist_desk
mkdir "%REDIST_DIR%" >nul 2>nul

:: 复制DLLs
xcopy /y /q "%BUILD_DIR%\src\rocky\Release\*.dll" "%REDIST_DIR%\"
echo DLLs 已复制

:: 复制LIB
xcopy /y /q "%BUILD_DIR%\src\rocky\Release\rocky.lib" "%REDIST_DIR%\"
echo LIB 已复制

:: 复制可执行文件
xcopy /y /q "%BUILD_DIR%\src\apps\rocky_simple\Release\rocky_simple.exe" "%REDIST_DIR%\"
xcopy /y /q "%BUILD_DIR%\src\apps\rocky_engine\Release\rocky_engine.exe" "%REDIST_DIR%\"
echo EXEs 已复制

:: 复制着色器文件
echo 复制着色器...
xcopy /s /y /q "%SRC_DIR%src\rocky\vsg\shaders" "%REDIST_DIR%\shaders\"
echo 着色器已复制

:: 复制字体文件
echo 复制字体...
xcopy /s /y /q "%SRC_DIR%data\fonts" "%REDIST_DIR%\fonts\"
echo 字体已复制

:: 复制PROJ数据文件
echo 复制PROJ数据...
xcopy /s /y /q "C:\dev\vcpkg\installed\x64-windows\share\proj" "%REDIST_DIR%\share\proj\"
echo PROJ数据已复制

echo.
echo 文件已复制到 %REDIST_DIR% 目录
echo.
echo 编译完成!
pause 