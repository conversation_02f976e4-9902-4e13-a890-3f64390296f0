{"name": "rocky", "version-string": "0.2", "port-version": 1, "description": "Rocky by Pelican Mapping", "homepage": "https://github.com/pelicanmapping/rocky", "supports": "!(x86 | wasm32)", "dependencies": ["cpp-httplib", "geographiclib", "geos", "entt", "glm", {"name": "imgui", "features": ["vulkan-binding"]}, "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "spdlog", "vsg", {"name": "vsgxchange", "features": ["assimp", "freetype"]}, "vsgqt"]}