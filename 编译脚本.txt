﻿（一）构建CMake编译目录

忘掉历史处理，忘掉memory，重新分析项目代码。
1.更新库源代码
2.对本项目使用Qt5和Visual Studio 2012编译，C++语法使用C++17。
Qt5安装目录C:/Qt/Qt5.14.2/5.14.2/msvc2017_64，
python安装目录在C:\Python\Python313，其余按照Rule规定执行

3.为了编译成轻量的库，
打开 ROCKY_SUPPORTS_HTTPLIB， 打开 ROCKY_SUPPORTS_QT，打开 ROCKY_SUPPORTS_IMGUI，打开ROCKY_SUPPORTS_HTTPS, 
关闭 ROCKY_SUPPORTS_CURL，关闭ROCKY_SUPPORTS_GDAL， 关闭 ROCKY_SUPPORTS_MBTILES

使用CMake构建编译目录到F:/rockyb



ROCKY_RENDERER_VSG: 是否启用 VSG/Vulkan 渲染后端。
ROCKY_SUPPORTS_HTTPLIB: 是否通过 cpp-httplib 库支持 HTTP。
ROCKY_SUPPORTS_CURL: 是否通过 CURL 库支持 HTTP（与 HTTPLIB 互斥）。
ROCKY_SUPPORTS_HTTPS: 是否支持 HTTPS，此选项依赖 OpenSSL。
ROCKY_SUPPORTS_GDAL: 是否支持 GeoTIFF、WMS、WMTS 等格式，此选项依赖 GDAL。
ROCKY_SUPPORTS_MBTILES: 是否支持 MBTiles 数据库（需要 sqlite3 和 zlib）。
ROCKY_SUPPORTS_AZURE: 是否支持 Azure Maps。
ROCKY_SUPPORTS_BING: 是否支持 Bing Maps。
ROCKY_SUPPORTS_IMGUI: 是否支持 Dear ImGui 并构建相关示例。
ROCKY_SUPPORTS_QT: 是否支持 Qt 并构建相关示例。



检查并列出代码中各种可以配置的宏参数，为了编译成轻量的库，
关闭对GDAL、openssl的依赖，重新使用CMake构建
打开 ROCKY_SUPPORTS_HTTPLIB， 打开 ROCKY_SUPPORTS_QT， 
关闭 ROCKY_SUPPORTS_CURL，关闭ROCKY_SUPPORTS_HTTPS，修正Qt模块配置，重新CMake


存在大量 qtVsg 的问题，经检查是由于依赖库是Qt5的原因

已经安装到 redist_desk 目录，且使用C:\Qt\Qt6.3.1\6.7.2\msvc2019_64\bin\windeployqt6.exe
完成了qt依赖库的复制，但运行应用程序时，报告缺失动态库： 
vsgXchange.dll，vsg-14.dll ，fmt.dll，spdlog.dll，proj_9.dll，zlib1.dll，
以及proj库所需要的参数数据目录，接下来请检查并修正脚本，确保程序能够正确运行

cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_TOOLCHAIN_FILE=../vcpkg/scripts/buildsystems/vcpkg.cmake -DQt6_DIR=C:/Qt/Qt6.7.2/msvc2022_64/lib/cmake/Qt6 -DQT_PACKAGE_NAME=Qt6 && cmake --install . --prefix ../redist_desk



cmake --build . --config Release --target rocky_demo
cd .. && cmake --install build_desk --config Release --prefix redist_desk