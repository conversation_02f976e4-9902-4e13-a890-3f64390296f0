name: windows-x64-Release-minimal

on: [push, pull_request, workflow_dispatch]

env:
  VCPKG_BINARY_SOURCES : 'clear;x-gha,readwrite'
  # Customize the CMake build type here (Release, Debug, RelWithDebInfo, etc.)
  BUILD_TYPE: Release
  VCPKG_VERSION: 'master'

jobs:

  build:
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: ['windows-latest']
        include:
          - os: 'windows-latest'
            triplet: 'x64-windows'
            mono: ''
            VCPKG_WORKSPACE: 'c:/vcpkg_own'

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive
        
    - name: Export GitHub Actions cache environment variables
      uses: actions/github-script@v7
      with:
        script: |
          core.exportVariable('ACTIONS_CACHE_URL', process.env.ACTIONS_CACHE_URL || '');
          core.exportVariable('ACTIONS_RUNTIME_TOKEN', process.env.ACTIONS_RUNTIME_TOKEN || '');

    - name: Install Vulkan SDK
      uses: pelicanmapping/setup-vulkan-sdk@v1.2.1
      with:
        vulkan-query-version: *********
        vulkan-components: Vulkan-Headers, Vulkan-Loader
        vulkan-use-cache: true

    - name: Installing vcpkg (windows)
      shell: 'bash'
      run: |
        cmake -E make_directory ${{ matrix.VCPKG_WORKSPACE }}
        cd ${{ matrix.VCPKG_WORKSPACE }}
        # git clone --depth 1 --branch ${{env.VCPKG_VERSION}} https://github.com/microsoft/vcpkg
        git clone https://github.com/microsoft/vcpkg
        cd vcpkg
        git checkout ${{env.VCPKG_VERSION}}
        cd ..
        ./vcpkg/bootstrap-vcpkg.bat -disableMetrics
        echo "set(VCPKG_BUILD_TYPE release)" >> ./vcpkg/triplets/x64-windows.cmake
        ${{ matrix.VCPKG_WORKSPACE }}/vcpkg/vcpkg version

    - name: Create Build Environment
      run: |
        cmake -E make_directory ${{runner.workspace}}/build

    - name: Configure CMake
      shell: bash
      working-directory: ${{ runner.workspace }}/build
      run: cmake $GITHUB_WORKSPACE -DWIN32_USE_MP=ON -DCMAKE_BUILD_TYPE=$BUILD_TYPE -DROCKY_SUPPORTS_GDAL=OFF -DROCKY_SUPPORTS_MBTILES=OFF -DROCKY_SUPPORTS_AZURE=OFF -DROCKY_SUPPORTS_BING=OFF -DCMAKE_TOOLCHAIN_FILE=${{ matrix.VCPKG_WORKSPACE }}/vcpkg/scripts/buildsystems/vcpkg.cmake -DVCPKG_BUILD_TYPE=$BUILD_TYPE -DVCPKG_MANIFEST_DIR=$GITHUB_WORKSPACE/vcpkg

    - name: 'Upload cmake configure log artifact'
      uses: actions/upload-artifact@v4
      if: ${{ failure() }}
      with:
        name: cmake-log
        path: ${{ runner.workspace }}/build/CMakeCache.txt
        retention-days: 1

    - name: Build
      working-directory: ${{ runner.workspace }}/build
      shell: bash
      run: cmake --build . --config $BUILD_TYPE
