/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#include "GeosAdapter.h"
#include "Log.h"

#ifdef ROCKY_HAS_GEOS
#include <geos_c.h>
#endif

#include <cstdarg>
#include <cmath>

using namespace ROCKY_NAMESPACE;

GeosAdapter::GeosAdapter() :
#ifdef ROCKY_HAS_GEOS
    _context(nullptr),
    _wktReader(nullptr),
    _wktWriter(nullptr),
#endif
    _initialized(false)
{
}

GeosAdapter::~GeosAdapter()
{
    finalize();
}

bool GeosAdapter::initialize()
{
#ifdef ROCKY_HAS_GEOS
    if (_initialized)
        return true;

    // 初始化GEOS库
    _context = GEOS_init_r();
    if (!_context)
    {
        Log()->error("Failed to initialize GEOS context");
        return false;
    }

    // 设置错误和警告处理器
    GEOSContext_setErrorHandler_r(_context, errorHandler);
    GEOSContext_setNoticeHandler_r(_context, warningHandler);

    // 创建WKT读写器
    _wktReader = GEOSWKTReader_create_r(_context);
    _wktWriter = GEOSWKTWriter_create_r(_context);

    if (!_wktReader || !_wktWriter)
    {
        Log()->error("Failed to create GEOS WKT readers/writers");
        finalize();
        return false;
    }

    _initialized = true;
    Log()->info("GEOS adapter initialized successfully");
    return true;
#else
    Log()->error("GEOS support not compiled in");
    return false;
#endif
}

void GeosAdapter::finalize()
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized)
        return;

    if (_wktWriter)
    {
        GEOSWKTWriter_destroy_r(_context, _wktWriter);
        _wktWriter = nullptr;
    }

    if (_wktReader)
    {
        GEOSWKTReader_destroy_r(_context, _wktReader);
        _wktReader = nullptr;
    }

    if (_context)
    {
        GEOS_finish_r(_context);
        _context = nullptr;
    }

    _initialized = false;
    Log()->info("GEOS adapter finalized");
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createPoint(double x, double y, double z)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized)
        return nullptr;

    GEOSCoordSequence* coords = GEOSCoordSeq_create_r(_context, 1, 3);
    GEOSCoordSeq_setX_r(_context, coords, 0, x);
    GEOSCoordSeq_setY_r(_context, coords, 0, y);
    GEOSCoordSeq_setZ_r(_context, coords, 0, z);

    GEOSGeometry* geosPoint = GEOSGeom_createPoint_r(_context, coords);
    return convertFromGEOS(geosPoint);
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createLineString(const std::vector<glm::dvec3>& points)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || points.empty())
        return nullptr;

    GEOSCoordSequence* coords = GEOSCoordSeq_create_r(_context, points.size(), 3);
    for (size_t i = 0; i < points.size(); ++i)
    {
        GEOSCoordSeq_setX_r(_context, coords, i, points[i].x);
        GEOSCoordSeq_setY_r(_context, coords, i, points[i].y);
        GEOSCoordSeq_setZ_r(_context, coords, i, points[i].z);
    }

    GEOSGeometry* geosLine = GEOSGeom_createLineString_r(_context, coords);
    return convertFromGEOS(geosLine);
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createPolygon(const std::vector<glm::dvec3>& exteriorRing,
                                                    const std::vector<std::vector<glm::dvec3>>& holes)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || exteriorRing.size() < 3)
        return nullptr;

    // 创建外环
    GEOSCoordSequence* extCoords = GEOSCoordSeq_create_r(_context, exteriorRing.size(), 3);
    for (size_t i = 0; i < exteriorRing.size(); ++i)
    {
        GEOSCoordSeq_setX_r(_context, extCoords, i, exteriorRing[i].x);
        GEOSCoordSeq_setY_r(_context, extCoords, i, exteriorRing[i].y);
        GEOSCoordSeq_setZ_r(_context, extCoords, i, exteriorRing[i].z);
    }
    GEOSGeometry* shell = GEOSGeom_createLinearRing_r(_context, extCoords);

    // 创建内环
    GEOSGeometry** holeGeoms = nullptr;
    int numHoles = holes.size();
    if (numHoles > 0)
    {
        holeGeoms = new GEOSGeometry*[numHoles];
        for (int h = 0; h < numHoles; ++h)
        {
            const auto& hole = holes[h];
            if (hole.size() >= 3)
            {
                GEOSCoordSequence* holeCoords = GEOSCoordSeq_create_r(_context, hole.size(), 3);
                for (size_t i = 0; i < hole.size(); ++i)
                {
                    GEOSCoordSeq_setX_r(_context, holeCoords, i, hole[i].x);
                    GEOSCoordSeq_setY_r(_context, holeCoords, i, hole[i].y);
                    GEOSCoordSeq_setZ_r(_context, holeCoords, i, hole[i].z);
                }
                holeGeoms[h] = GEOSGeom_createLinearRing_r(_context, holeCoords);
            }
        }
    }

    GEOSGeometry* geosPolygon = GEOSGeom_createPolygon_r(_context, shell, holeGeoms, numHoles);
    
    if (holeGeoms)
        delete[] holeGeoms;

    return convertFromGEOS(geosPolygon);
#else
    return nullptr;
#endif
}

std::shared_ptr<Geometry> GeosAdapter::createFromWKT(const std::string& wkt)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !_wktReader)
        return nullptr;

    GEOSGeometry* geosGeom = GEOSWKTReader_read_r(_context, _wktReader, wkt.c_str());
    return convertFromGEOS(geosGeom);
#else
    return nullptr;
#endif
}

std::string GeosAdapter::toWKT(const Geometry* geom)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !_wktWriter || !geom)
        return "";

    GEOSGeometry* geosGeom = static_cast<GEOSGeometry*>(convertToGEOS(geom));
    if (!geosGeom)
        return "";

    char* wktStr = GEOSWKTWriter_write_r(_context, _wktWriter, geosGeom);
    std::string result(wktStr ? wktStr : "");
    
    if (wktStr)
        GEOSFree_r(_context, wktStr);
    GEOSGeom_destroy_r(_context, geosGeom);

    return result;
#else
    return "";
#endif
}

bool GeosAdapter::isValid(const Geometry* geom)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom)
        return false;

    GEOSGeometry* geosGeom = static_cast<GEOSGeometry*>(convertToGEOS(geom));
    if (!geosGeom)
        return false;

    bool valid = GEOSisValid_r(_context, geosGeom) == 1;
    GEOSGeom_destroy_r(_context, geosGeom);
    return valid;
#else
    return false;
#endif
}

double GeosAdapter::getArea(const Geometry* geom)
{
#ifdef ROCKY_HAS_GEOS
    if (!_initialized || !geom)
        return 0.0;

    GEOSGeometry* geosGeom = static_cast<GEOSGeometry*>(convertToGEOS(geom));
    if (!geosGeom)
        return 0.0;

    double area = 0.0;
    GEOSArea_r(_context, geosGeom, &area);
    GEOSGeom_destroy_r(_context, geosGeom);
    return area;
#else
    return 0.0;
#endif
}

GeosAdapter& GeosAdapter::instance()
{
    static GeosAdapter s_instance;
    return s_instance;
}

std::shared_ptr<Geometry> GeosAdapter::convertFromGEOS(void* geosGeom)
{
    // 这里需要实现GEOS几何到Rocky几何的转换
    // 暂时返回空指针，需要根据Rocky的Geometry类结构来实现
    return nullptr;
}

void* GeosAdapter::convertToGEOS(const Geometry* geom)
{
    // 这里需要实现Rocky几何到GEOS几何的转换
    // 暂时返回空指针，需要根据Rocky的Geometry类结构来实现
    return nullptr;
}

void GeosAdapter::errorHandler(const char* fmt, ...)
{
    va_list args;
    va_start(args, fmt);
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), fmt, args);
    va_end(args);
    Log()->error("GEOS Error: {}", buffer);
}

void GeosAdapter::warningHandler(const char* fmt, ...)
{
    va_list args;
    va_start(args, fmt);
    char buffer[1024];
    vsnprintf(buffer, sizeof(buffer), fmt, args);
    va_end(args);
    Log()->warn("GEOS Warning: {}", buffer);
}

// GeosGeometryFactory implementation
std::shared_ptr<Geometry> GeosGeometryFactory::createRectangle(double minX, double minY, double maxX, double maxY)
{
    std::vector<glm::dvec3> points = {
        {minX, minY, 0.0},
        {maxX, minY, 0.0},
        {maxX, maxY, 0.0},
        {minX, maxY, 0.0},
        {minX, minY, 0.0}  // 闭合多边形
    };
    return GeosAdapter::instance().createPolygon(points);
}

std::shared_ptr<Geometry> GeosGeometryFactory::createCircle(double centerX, double centerY, double radius, int segments)
{
    std::vector<glm::dvec3> points;
    points.reserve(segments + 1);

    for (int i = 0; i <= segments; ++i)
    {
        double angle = 2.0 * M_PI * i / segments;
        double x = centerX + radius * std::cos(angle);
        double y = centerY + radius * std::sin(angle);
        points.emplace_back(x, y, 0.0);
    }

    return GeosAdapter::instance().createPolygon(points);
}

std::shared_ptr<Geometry> GeosGeometryFactory::createFromExtent(const GeoExtent& extent)
{
    return createRectangle(extent.west(), extent.south(), extent.east(), extent.north());
}
