adjust-args.patch e5c08f37fce7aadd4f6f6ee1994508f0255004df9811ccadfd8c533c684d93bc
adjust-python-dep.patch b334a262ff9687da0d39a7b1aceda866db9fe9229964263939f37edcb2006bc7
cmake 3.30.1
execute_process 66a937b9c074422643135c319d1abadaa45484a664f1b160d4c163efb444a446
features core
fix-libcpp-enable-assertions.patch 20811c4e861dc789e2885126febf4bb68fcb1e26b145e237486c8fdc23b35108
install.cmake f7f6e1bc2c4573d60db625ea9cd7d68b90d20856402cfde7edacfa7dbd99d338
meson-intl.patch f4a9ef5f1f25ba8a496aa5379cda5c20562500ba237af38eba687d0c37bc37cd
meson.template.in 8d48025a72e03c52058c43964b436a837e83a35be5f569111f0686d8dc53a495
portfile.cmake 67ca1de935d1f3b8f118cdb2c752e691c0a4207fca5914f3fbd2349650575447
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
remove-freebsd-pcfile-specialization.patch 21fd2398a7b7db5f68710e4eb8b3a2938e10064b137c50944773bad8fa14a0f2
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg-cmake-get-vars 5b9c6c71e45a0caaecd95d58aa8ebb05fdf46c1f4e2d8eb838ae0730e64bb7e4
vcpkg-port-config.cmake 65b1baa09892e06551a40319342add78030217868771a48d1520663d53b47302
vcpkg.json 680af8d46c14242e1e52e94cd7adb4133ae8b0a8bbf4b3992b8a6c74249ca7f7
vcpkg_add_to_path 5f5ae75cf37b2a58d1a8561ca96496b64cd91ec9a0afab0b976c3e5d59030bfe
vcpkg_apply_patches 4c123ce6f9bf0c46adc8287b7bd6a40b5f5e8daa83623dd21bb8c568ad4421fc
vcpkg_configure_meson 03e5ccf8e70e204690fae3a9b8ac40912179ae1822f2c5d58cac40f1d08ffb13
vcpkg_configure_meson.cmake a30278bc5be8f8bd18cddb241eede6ad2eb43be1093695fc9875a84cdb91c86e
vcpkg_download_distfile 5b22c0d0a397f4fdd0aa1c054afc84c20c91591d669027688b7c11334c3e4d0e
vcpkg_execute_in_download_mode 75a3cabe04d6bbaf1f73555516dd3c492edbed22cea204835c3620db5d948851
vcpkg_execute_required_process 2df167e6e7f37c8038e02a3ebbfb239fa55de0b384ed4532d33f5483710a608b
vcpkg_find_acquire_program 03722d714d388b19731f5c0be35996c25a47d5b35dd6a09669ea1e599a56e005
vcpkg_find_acquire_program(MESON) b39aa03a7f091406f39101eadd5ede4f893f01cc2a825422be9b7ad537e64a69
vcpkg_find_acquire_program(NINJA) ac757f414eae2c678cd541840807a2145bb84b4693f2ec5e46a630309e480c91
vcpkg_find_acquire_program(PKGCONFIG) 20ae00612ded560fad43b1af4a4b10c13ff4fbf0f9465f3f20214f3aed3b2237
vcpkg_find_acquire_program(PYTHON3) 756bad24d5fd0c492537f544a8f716356b4e70113ae3fb350c38b296204b108e
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
vcpkg_install_meson cc77e0248f5e935134c9061e5d424bc6533b561eeeac6f72d1788837cd5d0384
vcpkg_install_meson.cmake cc77e0248f5e935134c9061e5d424bc6533b561eeeac6f72d1788837cd5d0384
vcpkg_list f5de3ebcbc40a4db90622ade9aca918e2cf404dc0d91342fcde457d730e6fa29
vcpkg_replace_string b450deb79207478b37119743e00808ebc42de0628e7b98c14ab24728bd5c78b8
z_vcpkg_apply_patches aed81b9cb2c44e2736e6e912445044a2f0512c041ecbb72ffb3154e44b0044ba
z_vcpkg_function_arguments 11721a8a42a2aa2f6f285d485b21aed55834c2029140661bd8e2f0b709288d77
