{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/vsg-x64-windows-1.1.10-b991cb13-da27-4192-aaef-d6595abbdea1", "name": "vsg:x64-windows@1.1.10 75ed1d1386640fde06416b48f8e974d469ae80b9e1944318026979955a2f483c", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-02T09:01:10Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "vsg", "SPDXID": "SPDXRef-port", "versionInfo": "1.1.10", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/vsg", "homepage": "http://www.vulkanscenegraph.org/", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "A modern, cross platform, high performance scene graph library built upon Vulkan.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "vsg:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "75ed1d1386640fde06416b48f8e974d469ae80b9e1944318026979955a2f483c", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "vsg-dev/VulkanSceneGraph", "downloadLocation": "git+https://github.com/vsg-dev/VulkanSceneGraph@v1.1.10", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "eb32cc1418bbfd0907e7bc09080001b47f5c39a44b2693a2e3127a928d78a9e80ac4356b63fe4cd8bfb16f4bf829ea56eaaa0e878380fbfe06268962331cd86b"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "e75e8a4330fa8dd5c1a77cad46b6882c2de8a188482e35ee0e0a344477439619"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "883f56444f8e7182238c9b9931157520ab1549e10fe798274b371ccb5c96e87c"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}