{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/spdlog-x64-windows-1.15.3-b717ac4b-a532-4a59-a5ef-d4db83b56b17", "name": "spdlog:x64-windows@1.15.3 18dbe19575e5c2f8105d56cd473c62eabfb38ac5091f3289af23e9c485316681", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-02T08:51:11Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-2"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-2", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "spdlog", "SPDXID": "SPDXRef-port", "versionInfo": "1.15.3", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/spdlog", "homepage": "https://github.com/gabime/spdlog", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Very fast, header-only/compiled, C++ logging library.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "spdlog:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "18dbe19575e5c2f8105d56cd473c62eabfb38ac5091f3289af23e9c485316681", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "gabime/spdlog", "downloadLocation": "git+https://github.com/gabime/spdlog@v1.15.3", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "21c35f4091850ea3a0cd6a24867e06e943df70d76cd5a7ec0b15a33e0e9e0cc3584ed7930e1ac6f347e7e06f0e002d0e759884eaf05310014e24ea0e0419fcc4"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "d7f1082de0117b48de6e07088a87dbdd0e7c6433502dc3e6b0b2e754c87db2cb"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./usage", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "bdc67ce7fb7be263e7fdf3c57815f8f9f6142aca5d8f4da5e5597f25a6163bc6"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-2", "checksums": [{"algorithm": "SHA256", "checksumValue": "e8b1bebee55dafbef8ee43b71fb22d42d1b59daf8da29f17ff1e9dc880c97475"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}