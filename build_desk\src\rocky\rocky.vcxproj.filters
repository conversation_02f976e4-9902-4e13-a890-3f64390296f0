﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Azure.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\AzureImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\BingElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\BingImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Color.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Config.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Context.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\DateTime.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\ElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Ellipsoid.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Ephemeris.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Feature.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoCircle.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoExtent.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoHeightfield.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoImage.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoPoint.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Geocoder.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeographicLibAdapter.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Geoid.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Heightfield.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Horizon.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\IOTypes.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Image.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\ImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Layer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\LayerCollection.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\LibTIFFReader.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Log.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTiles.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTilesElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTilesImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Map.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Math.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Memory.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Profile.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\SRS.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Status.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMS.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMSElevationLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMSImageLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TerrainTileModel.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TerrainTileModelFactory.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Threading.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TileKey.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TileLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\URI.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Units.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Utils.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Viewpoint.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\VisibleLayer.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\static.cpp">
      <Filter>Sources</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\tinyxml\tinyxml.cpp">
      <Filter>Sources\tinyxml</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\tinyxml\tinyxmlerror.cpp">
      <Filter>Sources\tinyxml</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\tinyxml\tinyxmlparser.cpp">
      <Filter>Sources\tinyxml</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\contrib\EarthFileImporter.cpp">
      <Filter>Sources\contrib</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\Application.cpp">
      <Filter>Sources\vsg</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\DisplayManager.cpp">
      <Filter>Sources\vsg</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\GeoTransform.cpp">
      <Filter>Sources\vsg</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\MapManipulator.cpp">
      <Filter>Sources\vsg</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\MapNode.cpp">
      <Filter>Sources\vsg</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\NodePager.cpp">
      <Filter>Sources\vsg</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\PipelineState.cpp">
      <Filter>Sources\vsg</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\RTT.cpp">
      <Filter>Sources\vsg</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\SkyNode.cpp">
      <Filter>Sources\vsg</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\VSGContext.cpp">
      <Filter>Sources\vsg</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\GeometryPool.cpp">
      <Filter>Sources\vsg\terrain</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\SurfaceNode.cpp">
      <Filter>Sources\vsg\terrain</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainEngine.cpp">
      <Filter>Sources\vsg\terrain</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainNode.cpp">
      <Filter>Sources\vsg\terrain</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainSettings.cpp">
      <Filter>Sources\vsg\terrain</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainState.cpp">
      <Filter>Sources\vsg\terrain</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainTileNode.cpp">
      <Filter>Sources\vsg\terrain</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainTilePager.cpp">
      <Filter>Sources\vsg\terrain</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\ECSNode.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\FeatureView.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\IconSystem.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\IconSystem2.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\LabelSystem.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\LineSystem.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\MeshSystem.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Registry.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\TransformDetail.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\TransformSystem.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\WidgetSystem.cpp">
      <Filter>Sources\vsg\ecs</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\ImGuiIntegration.cpp">
      <Filter>Sources\vsg\imgui</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\RenderImGui.cpp">
      <Filter>Sources\vsg\imgui</Filter>
    </ClCompile>
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\SendEventsToImGui.cpp">
      <Filter>Sources\vsg\imgui</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Azure.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\AzureImageLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Bing.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\BingElevationLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\BingImageLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Callbacks.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Color.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Common.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Config.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Context.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\DateTime.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\ElevationLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Ellipsoid.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Ephemeris.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Feature.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoCircle.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoCommon.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoExtent.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoHeightfield.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoImage.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoPoint.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Geocoder.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeographicLibAdapter.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Geoid.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Heightfield.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Horizon.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\IOTypes.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Image.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\ImageLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\LRUCache.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Layer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\LayerCollection.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\LayerReference.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\LibTIFFReader.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Log.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTiles.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTilesElevationLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTilesImageLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Map.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Math.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Memory.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Profile.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\SRS.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\SentryTracker.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Status.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMS.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMSElevationLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMSImageLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TerrainTileModel.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TerrainTileModelFactory.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Threading.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TileKey.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TileLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\URI.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Units.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Utils.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Viewpoint.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\VisibleLayer.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\json.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\option.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rocky.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rtree.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\sha1.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\tinyxml\tinyxml.h">
      <Filter>Headers\tinyxml</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\weejobs.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\weemesh.h">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Version.h.in">
      <Filter>Headers</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky\Version.h">
      <Filter>Headers\..\..\build_desk\build_include\rocky</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\contrib\EarthFileImporter.h">
      <Filter>Headers\contrib</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\Application.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\Common.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\DisplayManager.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\GeoTransform.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\MapManipulator.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\MapNode.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\NodePager.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\PipelineState.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\PixelScaleTransform.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\RTT.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\SkyNode.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\Utils.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\VSGContext.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ViewLocal.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\json.h">
      <Filter>Headers\vsg</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\GeometryPool.h">
      <Filter>Headers\vsg\terrain</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\SurfaceNode.h">
      <Filter>Headers\vsg\terrain</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainEngine.h">
      <Filter>Headers\vsg\terrain</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainNode.h">
      <Filter>Headers\vsg\terrain</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainSettings.h">
      <Filter>Headers\vsg\terrain</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainState.h">
      <Filter>Headers\vsg\terrain</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainTileHost.h">
      <Filter>Headers\vsg\terrain</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainTileNode.h">
      <Filter>Headers\vsg\terrain</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainTilePager.h">
      <Filter>Headers\vsg\terrain</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Component.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Declutter.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\ECSNode.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\FeatureView.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Icon.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\IconSystem.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\IconSystem2.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Label.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\LabelSystem.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Line.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\LineSystem.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Mesh.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\MeshSystem.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Motion.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\MotionSystem.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Registry.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Transform.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\TransformDetail.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\TransformSystem.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Visibility.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Widget.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\WidgetSystem.h">
      <Filter>Headers\vsg\ecs</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\ImGuiIntegration.h">
      <Filter>Headers\vsg\imgui</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\RenderImGui.h">
      <Filter>Headers\vsg\imgui</Filter>
    </ClInclude>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\SendEventsToImGui.h">
      <Filter>Headers\vsg\imgui</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Natvis Include="C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_json.natvis" />
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.atmo.ground.vert.glsl">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.atmo.sky.frag">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.atmo.sky.vert">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.icon.frag">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.icon.indirect.cull.comp">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.icon.indirect.frag">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.icon.indirect.vert">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.icon.vert">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.lighting.frag.glsl">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.line.frag">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.line.vert">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.mesh.frag">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.mesh.vert">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.terrain.frag">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.terrain.vert">
      <Filter>Shaders\vsg\shaders</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Headers">
      <UniqueIdentifier>{C2DF1122-9808-383B-A650-E0AD88DE6570}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\..">
      <UniqueIdentifier>{BD102073-7EF1-359F-87EE-97F73A66FDCD}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\..\..">
      <UniqueIdentifier>{D285F414-57B7-311A-B259-E2DBEA5E5F5F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\..\..\build_desk">
      <UniqueIdentifier>{5CC80B15-FAD3-3B7C-9D37-EA892B6F48CD}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\..\..\build_desk\build_include">
      <UniqueIdentifier>{436CC18C-4CF2-315B-BB8C-CCA5C00FD078}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\..\..\build_desk\build_include\rocky">
      <UniqueIdentifier>{6B649FD0-41D9-3250-AAFF-F87BF5B75FC7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\contrib">
      <UniqueIdentifier>{135B179D-0CA1-34C6-ACA4-357DC3BEC6CA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\tinyxml">
      <UniqueIdentifier>{794684B4-99C5-389D-9250-1B8224443FB0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\vsg">
      <UniqueIdentifier>{4323F818-8195-3656-A4B1-5F2EE74D8523}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\vsg\ecs">
      <UniqueIdentifier>{DF8AE172-439A-3869-A73D-D0E5DE42D39C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\vsg\imgui">
      <UniqueIdentifier>{74B7004D-3880-3E74-B4C5-74FF7BE6AEE8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Headers\vsg\terrain">
      <UniqueIdentifier>{B2559A3F-6989-3749-84C7-EFFDD350517D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Shaders">
      <UniqueIdentifier>{F9BAEC0C-548B-3DF0-AA72-3A3648AC9481}</UniqueIdentifier>
    </Filter>
    <Filter Include="Shaders\vsg">
      <UniqueIdentifier>{2B2B0BB9-5DC3-3FEA-8026-A76A682815CA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Shaders\vsg\shaders">
      <UniqueIdentifier>{6134D283-CF93-36FC-9506-E75E22D14F10}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources">
      <UniqueIdentifier>{E94243AE-392A-32C8-8438-9DAF02EAFC7E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\contrib">
      <UniqueIdentifier>{09936ADF-E6B5-3836-A1F0-037D55D72167}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\tinyxml">
      <UniqueIdentifier>{B9DA054A-D2ED-3590-8FF9-E5A56944AFBE}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\vsg">
      <UniqueIdentifier>{5AD3BDAC-1985-34B1-A117-20CD36B0E942}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\vsg\ecs">
      <UniqueIdentifier>{FB724CBA-00FA-3866-8537-2E19992A03B8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\vsg\imgui">
      <UniqueIdentifier>{5F1F704C-EF62-3CDB-A396-EC3DD8E541D3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sources\vsg\terrain">
      <UniqueIdentifier>{D46CAF50-3B79-3AE9-B717-0F67D4FCD208}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
