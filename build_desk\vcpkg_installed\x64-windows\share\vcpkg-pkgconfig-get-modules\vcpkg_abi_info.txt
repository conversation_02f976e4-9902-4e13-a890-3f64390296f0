cmake 3.30.1
execute_process 66a937b9c074422643135c319d1abadaa45484a664f1b160d4c163efb444a446
features core
pkgconf d2de46e042cc0bdbbae7e3adcd3a38ca57d2b188cca2d86bd998c911374fad31
portfile.cmake 428b5ca9c0a3de8e486ee5b624c1fa58f8e77f91c6efc3b2c91b90ed125b6787
ports.cmake 396a7ff92b4f63d694e861eda4041784616a05ca31268739cac927c0ac9c4843
post_build_checks 2
powershell 7.5.1
triplet x64-windows
triplet_abi 4556164a2cd3dd6f4742101eabb46def7e71b6e5856faa88e5d005aac12a803c-1d3d767ab10b488977977b83f6c2da7e06afd80b1e375a2a41c60c343e7b3f94-9322494aa025c04635f453cbc392b0ca170d297c
vcpkg-port-config.cmake af32777ec0921985699f91ee097b889e32813aede0bafc860521f11e47571cdc
vcpkg.json 1ab83a963e761a37e14f35df5d0d34ddd4ddb474977a8a67e160134878e264f5
vcpkg_install_copyright ba6c169ab4e59fa05682e530cdeb883767de22c8391f023d4e6844a7ec5dd3d2
x_vcpkg_pkgconfig_get_modules.cmake 322ee8af0f60819b32012525b7f7cc563addb0a32954206f66722a2af4139739
