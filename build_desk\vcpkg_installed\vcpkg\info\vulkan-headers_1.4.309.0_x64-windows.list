x64-windows/
x64-windows/include/
x64-windows/include/vk_video/
x64-windows/include/vk_video/vulkan_video_codec_av1std.h
x64-windows/include/vk_video/vulkan_video_codec_av1std_decode.h
x64-windows/include/vk_video/vulkan_video_codec_av1std_encode.h
x64-windows/include/vk_video/vulkan_video_codec_h264std.h
x64-windows/include/vk_video/vulkan_video_codec_h264std_decode.h
x64-windows/include/vk_video/vulkan_video_codec_h264std_encode.h
x64-windows/include/vk_video/vulkan_video_codec_h265std.h
x64-windows/include/vk_video/vulkan_video_codec_h265std_decode.h
x64-windows/include/vk_video/vulkan_video_codec_h265std_encode.h
x64-windows/include/vk_video/vulkan_video_codecs_common.h
x64-windows/include/vulkan/
x64-windows/include/vulkan/vk_icd.h
x64-windows/include/vulkan/vk_layer.h
x64-windows/include/vulkan/vk_platform.h
x64-windows/include/vulkan/vulkan.cppm
x64-windows/include/vulkan/vulkan.h
x64-windows/include/vulkan/vulkan.hpp
x64-windows/include/vulkan/vulkan_android.h
x64-windows/include/vulkan/vulkan_beta.h
x64-windows/include/vulkan/vulkan_core.h
x64-windows/include/vulkan/vulkan_directfb.h
x64-windows/include/vulkan/vulkan_enums.hpp
x64-windows/include/vulkan/vulkan_extension_inspection.hpp
x64-windows/include/vulkan/vulkan_format_traits.hpp
x64-windows/include/vulkan/vulkan_fuchsia.h
x64-windows/include/vulkan/vulkan_funcs.hpp
x64-windows/include/vulkan/vulkan_ggp.h
x64-windows/include/vulkan/vulkan_handles.hpp
x64-windows/include/vulkan/vulkan_hash.hpp
x64-windows/include/vulkan/vulkan_hpp_macros.hpp
x64-windows/include/vulkan/vulkan_ios.h
x64-windows/include/vulkan/vulkan_macos.h
x64-windows/include/vulkan/vulkan_metal.h
x64-windows/include/vulkan/vulkan_raii.hpp
x64-windows/include/vulkan/vulkan_screen.h
x64-windows/include/vulkan/vulkan_shared.hpp
x64-windows/include/vulkan/vulkan_static_assertions.hpp
x64-windows/include/vulkan/vulkan_structs.hpp
x64-windows/include/vulkan/vulkan_to_string.hpp
x64-windows/include/vulkan/vulkan_vi.h
x64-windows/include/vulkan/vulkan_video.hpp
x64-windows/include/vulkan/vulkan_wayland.h
x64-windows/include/vulkan/vulkan_win32.h
x64-windows/include/vulkan/vulkan_xcb.h
x64-windows/include/vulkan/vulkan_xlib.h
x64-windows/include/vulkan/vulkan_xlib_xrandr.h
x64-windows/share/
x64-windows/share/cmake/
x64-windows/share/cmake/VulkanHeaders/
x64-windows/share/cmake/VulkanHeaders/VulkanHeadersConfig.cmake
x64-windows/share/cmake/VulkanHeaders/VulkanHeadersConfigVersion.cmake
x64-windows/share/vulkan-headers/
x64-windows/share/vulkan-headers/copyright
x64-windows/share/vulkan-headers/usage
x64-windows/share/vulkan-headers/vcpkg.spdx.json
x64-windows/share/vulkan-headers/vcpkg_abi_info.txt
x64-windows/share/vulkan/
x64-windows/share/vulkan/registry/
x64-windows/share/vulkan/registry/apiconventions.py
x64-windows/share/vulkan/registry/base_generator.py
x64-windows/share/vulkan/registry/cgenerator.py
x64-windows/share/vulkan/registry/generator.py
x64-windows/share/vulkan/registry/parse_dependency.py
x64-windows/share/vulkan/registry/profiles/
x64-windows/share/vulkan/registry/profiles/VP_KHR_roadmap.json
x64-windows/share/vulkan/registry/reg.py
x64-windows/share/vulkan/registry/spec_tools/
x64-windows/share/vulkan/registry/spec_tools/conventions.py
x64-windows/share/vulkan/registry/spec_tools/util.py
x64-windows/share/vulkan/registry/stripAPI.py
x64-windows/share/vulkan/registry/validusage.json
x64-windows/share/vulkan/registry/video.xml
x64-windows/share/vulkan/registry/vk.xml
x64-windows/share/vulkan/registry/vkconventions.py
x64-windows/share/vulkan/registry/vulkan_object.py
