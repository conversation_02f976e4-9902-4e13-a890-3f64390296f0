x64-windows/
x64-windows/debug/
x64-windows/debug/lib/
x64-windows/debug/lib/GenericCodeGend.lib
x64-windows/debug/lib/MachineIndependentd.lib
x64-windows/debug/lib/OSDependentd.lib
x64-windows/debug/lib/SPIRVd.lib
x64-windows/debug/lib/SPVRemapperd.lib
x64-windows/debug/lib/glslang-default-resource-limitsd.lib
x64-windows/debug/lib/glslangd.lib
x64-windows/include/
x64-windows/include/glslang/
x64-windows/include/glslang/Include/
x64-windows/include/glslang/Include/ResourceLimits.h
x64-windows/include/glslang/Include/glslang_c_interface.h
x64-windows/include/glslang/Include/glslang_c_shader_types.h
x64-windows/include/glslang/Include/visibility.h
x64-windows/include/glslang/MachineIndependent/
x64-windows/include/glslang/MachineIndependent/Versions.h
x64-windows/include/glslang/Public/
x64-windows/include/glslang/Public/ResourceLimits.h
x64-windows/include/glslang/Public/ShaderLang.h
x64-windows/include/glslang/Public/resource_limits_c.h
x64-windows/include/glslang/SPIRV/
x64-windows/include/glslang/SPIRV/GlslangToSpv.h
x64-windows/include/glslang/SPIRV/Logger.h
x64-windows/include/glslang/SPIRV/SPVRemapper.h
x64-windows/include/glslang/SPIRV/SpvTools.h
x64-windows/include/glslang/SPIRV/disassemble.h
x64-windows/include/glslang/SPIRV/spirv.hpp
x64-windows/include/glslang/build_info.h
x64-windows/lib/
x64-windows/lib/GenericCodeGen.lib
x64-windows/lib/MachineIndependent.lib
x64-windows/lib/OSDependent.lib
x64-windows/lib/SPIRV.lib
x64-windows/lib/SPVRemapper.lib
x64-windows/lib/glslang-default-resource-limits.lib
x64-windows/lib/glslang.lib
x64-windows/share/
x64-windows/share/glslang/
x64-windows/share/glslang/copyright
x64-windows/share/glslang/glslang-config-version.cmake
x64-windows/share/glslang/glslang-config.cmake
x64-windows/share/glslang/glslang-targets-debug.cmake
x64-windows/share/glslang/glslang-targets-release.cmake
x64-windows/share/glslang/glslang-targets.cmake
x64-windows/share/glslang/usage
x64-windows/share/glslang/vcpkg.spdx.json
x64-windows/share/glslang/vcpkg_abi_info.txt
