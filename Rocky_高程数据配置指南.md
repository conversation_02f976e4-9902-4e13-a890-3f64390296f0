# Rocky 高程数据配置指南

## 问题分析

### ReadyMap高程服务分析

您提供的XML显示ReadyMap是一个标准的TMS (Tile Map Service)：

```xml
<TileMap version="1.0.0" tilemapservice="https://readymap.org/readymap/tiles/1.0.0/">
<Title>ReadyMap 90m Elevation</Title>
<Abstract>CGIAR SRTM Elevation Composite</Abstract>
<SRS>EPSG:4326</SRS>
<TileFormat width="257" height="257" mime-type="image/tif" extension="tif"/>
```

**关键信息**:
- 数据格式: TIFF格式高程瓦片 (`image/tif`)
- 坐标系: EPSG:4326 (WGS84地理坐标系)
- 瓦片大小: 257x257像素
- URL模式: `https://readymap.org/readymap/tiles/1.0.0/116/{z}/{x}/{y}.tif`

### 核心问题

1. **HTTPS重定向**: ReadyMap将所有HTTP请求重定向到HTTPS
2. **Rocky限制**: Rocky引擎不支持HTTPS协议
3. **服务依赖**: 大多数现代地理数据服务都要求HTTPS

## 解决方案

### 方案1: 本地高程数据 (推荐)

**步骤1: 下载高程数据**
```bash
# 使用GDAL工具下载并转换高程数据
gdal_translate -of GTiff -co TILED=YES -co COMPRESS=LZW \
  "https://readymap.org/readymap/tiles/1.0.0/116/0/0/0.tif" \
  "redist_desk/data/elevation/0/0/0.tif"
```

**步骤2: 配置本地高程层**
```cpp
auto elevation = rocky::TMSElevationLayer::create();
elevation->setName("Local Elevation");
elevation->uri = "file://data/elevation/{z}/{x}/{y}.tif";
elevation->profile = rocky::Profile("global-geodetic");
elevation->minLevel.set_default(0u);
elevation->maxLevel.set_default(10u);
layers.add(elevation);
```

### 方案2: HTTP代理服务

创建一个本地HTTP代理，将HTTPS请求转换为HTTP：

**代理服务器 (Python示例)**:
```python
from http.server import HTTPServer, BaseHTTPRequestHandler
import requests

class ProxyHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        # 将HTTP请求转换为HTTPS
        https_url = f"https://readymap.org{self.path}"
        response = requests.get(https_url)
        
        self.send_response(200)
        self.send_header('Content-Type', response.headers.get('Content-Type'))
        self.end_headers()
        self.wfile.write(response.content)

server = HTTPServer(('localhost', 8080), ProxyHandler)
server.serve_forever()
```

**Rocky配置**:
```cpp
elevation->uri = "http://localhost:8080/readymap/tiles/1.0.0/116/{z}/{x}/{y}.tif";
```

### 方案3: 替代高程服务

寻找支持HTTP的高程数据服务：

```cpp
// 示例：使用其他HTTP高程服务
auto elevation = rocky::TMSElevationLayer::create();
elevation->setName("Alternative Elevation");
elevation->uri = "http://example-elevation-service.com/{z}/{x}/{y}.png";
elevation->profile = rocky::Profile("spherical-mercator");
```

### 方案4: 离线瓦片缓存

**步骤1: 创建瓦片缓存**
```bash
# 使用TileCache或类似工具预下载瓦片
mkdir -p redist_desk/cache/elevation
# 下载所需的瓦片到本地目录
```

**步骤2: 配置本地缓存**
```cpp
elevation->uri = "file://cache/elevation/{z}/{x}/{y}.tif";
```

## 当前实现状态

### ✅ 已实现
1. **URL打印**: 显示当前使用的影像URL
   ```
   [rocky info] Imagery URL: http://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}
   ```

2. **问题说明**: 清楚解释高程数据限制
   ```
   [rocky info] Note: Elevation data skipped - most services require HTTPS which Rocky doesn't support
   [rocky info] For 3D terrain, use local elevation files or compatible HTTP services
   ```

3. **基础地图**: 谷歌卫星影像正常工作

### ⚠️ 待实现
1. **3D地形**: 需要配置可用的高程数据源
2. **立体效果**: 依赖于高程数据的可用性

## 推荐的生产配置

### 完整的本地高程数据配置

```cpp
// 在rocky_demo.cpp中添加本地高程数据
if (std::filesystem::exists("data/elevation/0/0/0.tif")) {
    auto elevation = rocky::TMSElevationLayer::create();
    elevation->setName("Local SRTM Elevation");
    elevation->uri = "file://data/elevation/{z}/{x}/{y}.tif";
    elevation->profile = rocky::Profile("global-geodetic");
    elevation->minLevel.set_default(0u);
    elevation->maxLevel.set_default(10u);
    layers.add(elevation);
    
    rocky::Log()->info("Using local elevation data: {}", elevation->uri.value().full());
} else {
    rocky::Log()->info("No local elevation data found - running in 2D mode");
}
```

### 目录结构
```
redist_desk/
├── data/
│   ├── elevation/
│   │   ├── 0/0/0.tif
│   │   ├── 1/0/0.tif
│   │   └── ... (更多瓦片)
│   ├── icons/
│   └── models/
├── fonts/
├── shaders/
└── rocky_demo.exe
```

## 高程数据格式说明

### TMS格式特点
- **瓦片坐标**: {z}/{x}/{y} (缩放级别/X坐标/Y坐标)
- **数据格式**: 通常为GeoTIFF或PNG
- **高程编码**: 
  - GeoTIFF: 直接存储高程值
  - PNG: 使用RGB编码高程值

### 坐标系统
- **global-geodetic**: EPSG:4326, 适用于全球数据
- **spherical-mercator**: EPSG:3857, 适用于Web地图

## 总结

目前Rocky Demo已经可以正常运行，显示2D地球影像。要实现3D立体效果，需要：

1. **短期解决方案**: 使用HTTP代理或下载少量测试瓦片
2. **长期解决方案**: 建立完整的本地高程数据缓存
3. **生产环境**: 使用专业的离线地图数据解决方案

程序现在已经具备了完整的框架，只需要添加合适的高程数据源即可实现3D地形显示。
