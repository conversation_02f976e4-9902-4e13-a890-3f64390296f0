@echo off
echo 正在配置Rocky项目...
echo 使用Visual Studio 2022 和 Qt5.14.2
echo 编译目录: F:/rockyb2
echo.

:: 设置Qt5环境变量
set Qt5_DIR=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5
set PATH=%PATH%;C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/bin

:: 创建编译目录
if not exist "F:/rockyb2" mkdir "F:/rockyb2"

:: 进入编译目录
cd /d "F:/rockyb2"

:: 使用CMake配置项目
cmake -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_TOOLCHAIN_FILE=C:/dev/vcpkg/scripts/buildsystems/vcpkg.cmake ^
    -DVCPKG_TARGET_TRIPLET=x64-windows ^
    -DROCKY_SUPPORTS_HTTPLIB=ON ^
    -DROCKY_SUPPORTS_QT=ON ^
    -DROCKY_SUPPORTS_IMGUI=ON ^
    -DROCKY_SUPPORTS_CURL=OFF ^
    -DROCKY_SUPPORTS_HTTPS=OFF ^
    -DROCKY_SUPPORTS_MBTILES=OFF ^
    -DQt5_DIR=C:/Qt/Qt5.14.2/5.14.2/msvc2017_64/lib/cmake/Qt5 ^
    %~dp0

if %errorlevel% neq 0 (
    echo CMake配置失败！
    pause
    exit /b 1
)

echo.
echo CMake配置成功！
echo 编译目录: F:/rockyb2
echo.
echo 要编译项目，请运行：
echo cmake --build F:/rockyb2 --config Release
echo.
pause 