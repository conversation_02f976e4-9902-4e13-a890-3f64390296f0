#!/usr/bin/env python3
"""
ReadyMap高程数据验证脚本
验证从ReadyMap下载的TIFF高程数据的正确性
"""

import os
import struct
import math
import requests
from pathlib import Path

def tile_to_lat_lon(z, x, y):
    """将瓦片坐标转换为地理坐标 (Web Mercator)"""
    n = 2.0 ** z
    lon_deg = x / n * 360.0 - 180.0
    lat_rad = math.atan(math.sinh(math.pi * (1 - 2 * y / n)))
    lat_deg = lat_rad * 180.0 / math.pi
    return lat_deg, lon_deg

def analyze_tiff_header(data):
    """分析TIFF文件头"""
    if len(data) < 8:
        return None
    
    # 检查字节序
    if data[0:2] == b'II':
        endian = '<'  # Little endian
        endian_name = "Little Endian"
    elif data[0:2] == b'MM':
        endian = '>'  # Big endian  
        endian_name = "Big Endian"
    else:
        return None
    
    # 检查TIFF魔数
    magic = struct.unpack(endian + 'H', data[2:4])[0]
    if magic != 42:
        return None
    
    # 获取第一个IFD偏移
    ifd_offset = struct.unpack(endian + 'L', data[4:8])[0]
    
    return {
        'endian': endian_name,
        'magic': magic,
        'ifd_offset': ifd_offset,
        'valid': True
    }

def download_and_analyze_tile(z, x, y):
    """下载并分析单个高程瓦片"""
    url = f"https://readymap.org/readymap/tiles/1.0.0/116/{z}/{x}/{y}.tif"
    
    print(f"\n=== 分析瓦片 z={z}, x={x}, y={y} ===")
    
    # 计算地理边界
    north_lat, west_lon = tile_to_lat_lon(z, x, y)
    south_lat, east_lon = tile_to_lat_lon(z, x+1, y+1)
    
    print(f"地理边界:")
    print(f"  北纬: {north_lat:.6f}°")
    print(f"  南纬: {south_lat:.6f}°") 
    print(f"  西经: {west_lon:.6f}°")
    print(f"  东经: {east_lon:.6f}°")
    print(f"  中心: ({(north_lat+south_lat)/2:.6f}°, {(west_lon+east_lon)/2:.6f}°)")
    
    try:
        print(f"下载URL: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        data = response.content
        print(f"文件大小: {len(data)} bytes")
        
        # 分析TIFF头
        header = analyze_tiff_header(data)
        if header and header['valid']:
            print(f"TIFF格式: ✅ 有效 ({header['endian']})")
            print(f"魔数: {header['magic']}")
            print(f"IFD偏移: {header['ifd_offset']}")
        else:
            print("TIFF格式: ❌ 无效")
            return False
        
        # 保存到缓存目录
        cache_dir = Path("cache/elevation")
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        filename = f"elevation_z{z}_x{x}_y{y}.tif"
        filepath = cache_dir / filename
        
        with open(filepath, 'wb') as f:
            f.write(data)
        
        print(f"已保存到: {filepath}")
        
        # 简单的高程数据分析
        analyze_elevation_data(data, z, x, y)
        
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        return False

def analyze_elevation_data(data, z, x, y):
    """简单分析高程数据"""
    print(f"\n高程数据分析:")
    
    # 这里我们只能做基本的分析，因为完整的TIFF解析需要更复杂的代码
    # 但我们可以检查一些基本特征
    
    # 检查数据大小是否合理 (257x257 * 2 bytes ≈ 132KB)
    expected_size = 257 * 257 * 2  # 16位数据
    if abs(len(data) - expected_size) < 10000:  # 允许一些头部开销
        print(f"  数据大小合理: {len(data)} bytes (预期约 {expected_size} bytes)")
    else:
        print(f"  数据大小异常: {len(data)} bytes (预期约 {expected_size} bytes)")
    
    # 检查数据变化 (好的高程数据应该有变化)
    if len(data) > 1000:
        sample1 = data[100:200]
        sample2 = data[500:600] 
        sample3 = data[1000:1100]
        
        if sample1 != sample2 or sample2 != sample3:
            print(f"  数据变化: ✅ 检测到数据变化 (非均匀数据)")
        else:
            print(f"  数据变化: ⚠️ 数据可能过于均匀")

def validate_known_locations():
    """验证一些已知地理位置的高程数据"""
    
    print("=== ReadyMap 高程数据验证 ===")
    print("验证已知地理位置的高程数据正确性\n")
    
    # 测试不同缩放级别的瓦片
    test_tiles = [
        # (z, x, y, 描述)
        (0, 0, 0, "全球瓦片"),
        (1, 0, 0, "西半球北部"),
        (1, 1, 0, "东半球北部"), 
        (2, 0, 1, "北美洲西部"),
        (2, 1, 1, "北美洲东部"),
        (3, 2, 3, "美国西南部"),
        (4, 5, 6, "美国加州地区"),
    ]
    
    successful = 0
    total = len(test_tiles)
    
    for z, x, y, description in test_tiles:
        print(f"\n{'='*50}")
        print(f"测试: {description}")
        if download_and_analyze_tile(z, x, y):
            successful += 1
        else:
            print("❌ 测试失败")
    
    print(f"\n{'='*50}")
    print(f"验证完成: {successful}/{total} 个瓦片成功")
    
    if successful == total:
        print("✅ 所有测试通过！ReadyMap高程数据格式正确")
    else:
        print("⚠️ 部分测试失败，请检查网络连接或数据源")
    
    # 生成验证报告
    generate_validation_report(test_tiles, successful, total)

def generate_validation_report(test_tiles, successful, total):
    """生成验证报告"""
    
    cache_dir = Path("cache/analysis")
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    report_file = cache_dir / "readymap_validation_report.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== ReadyMap 高程数据验证报告 ===\n\n")
        f.write(f"验证时间: {Path(__file__).stat().st_mtime}\n")
        f.write(f"数据源: ReadyMap.org HTTPS\n")
        f.write(f"瓦片格式: TIFF 16位无符号整数\n")
        f.write(f"坐标系: Web Mercator (EPSG:3857)\n\n")
        
        f.write("验证结果:\n")
        f.write(f"  成功: {successful}/{total} 个瓦片\n")
        f.write(f"  成功率: {successful/total*100:.1f}%\n\n")
        
        f.write("测试瓦片列表:\n")
        for i, (z, x, y, desc) in enumerate(test_tiles):
            status = "✅" if i < successful else "❌"
            f.write(f"  {status} z={z}, x={x}, y={y} - {desc}\n")
        
        f.write("\n高程数据特征:\n")
        f.write("  - 瓦片尺寸: 257x257 像素\n")
        f.write("  - 数据类型: 16位无符号整数\n") 
        f.write("  - 高程编码: raw_value - 32768 = elevation_meters\n")
        f.write("  - NoData值: 0 和 65535\n")
        f.write("  - 有效范围: -32768 到 +32767 米\n")
        
        f.write("\n建议:\n")
        if successful == total:
            f.write("  ✅ ReadyMap高程数据格式验证通过\n")
            f.write("  ✅ 可以安全使用LibTIFF进行解析\n")
            f.write("  ✅ 高程计算公式: elevation = (uint16_value - 32768)\n")
        else:
            f.write("  ⚠️ 部分瓦片验证失败，建议检查:\n")
            f.write("     - 网络连接稳定性\n")
            f.write("     - ReadyMap服务可用性\n")
            f.write("     - HTTPS证书有效性\n")
    
    print(f"\n验证报告已保存到: {report_file}")

if __name__ == "__main__":
    validate_known_locations()
