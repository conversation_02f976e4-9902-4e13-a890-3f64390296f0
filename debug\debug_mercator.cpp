#include <rocky/SRS.h>
#include <rocky/GeoPoint.h>
#include <iostream>
#include <iomanip>
#include <cmath>

using namespace rocky;

int main() {
    std::cout << "Web Mercator 坐标转换调试" << std::endl;
    
    SRS wgs84("epsg:4326");
    SRS merc("epsg:3857");
    
    std::cout << "WGS84 valid: " << wgs84.valid() << std::endl;
    std::cout << "Mercator valid: " << merc.valid() << std::endl;
    
    if (!wgs84.valid() || !merc.valid()) {
        std::cout << "坐标系无效" << std::endl;
        return 1;
    }
    
    // 测试关键边界点
    std::cout << std::fixed << std::setprecision(12);
    
    // 测试 -180度经度
    std::cout << "\n=== 测试 -180度经度 ===" << std::endl;
    GeoPoint west180(wgs84, -180.0, 0.0, 0.0);
    GeoPoint mercWest = west180.transform(merc);
    std::cout << "(-180°, 0°) -> (" << mercWest.x << ", " << mercWest.y << ")" << std::endl;
    std::cout << "期望X: -20037508.342789244" << std::endl;
    
    // 反向转换
    GeoPoint backWest = mercWest.transform(wgs84);
    std::cout << "反向转换: (" << backWest.x << ", " << backWest.y << ")" << std::endl;
    std::cout << "期望: (-180°, 0°)" << std::endl;
    
    // 测试具体的边界值
    std::cout << "\n=== 测试具体边界值 ===" << std::endl;
    GeoPoint mercBoundary(merc, -20037508.34278925, 0.0, 0.0);
    GeoPoint wgsBoundary = mercBoundary.transform(wgs84);
    std::cout << "(-20037508.34278925, 0) -> (" << wgsBoundary.x << ", " << wgsBoundary.y << ")" << std::endl;
    std::cout << "期望: (-180°, 0°)" << std::endl;
    
    // 检查误差
    double lonError = std::abs(wgsBoundary.x - (-180.0));
    double latError = std::abs(wgsBoundary.y - 0.0);
    std::cout << "经度误差: " << std::scientific << lonError << "°" << std::endl;
    std::cout << "纬度误差: " << std::scientific << latError << "°" << std::endl;
    
    // 测试其他关键点
    std::cout << "\n=== 测试其他关键点 ===" << std::endl;
    
    // 原点
    GeoPoint origin(wgs84, 0.0, 0.0, 0.0);
    GeoPoint mercOrigin = origin.transform(merc);
    std::cout << "(0°, 0°) -> (" << mercOrigin.x << ", " << mercOrigin.y << ")" << std::endl;
    
    // 180度
    GeoPoint east180(wgs84, 180.0, 0.0, 0.0);
    GeoPoint mercEast = east180.transform(merc);
    std::cout << "(180°, 0°) -> (" << mercEast.x << ", " << mercEast.y << ")" << std::endl;
    std::cout << "期望X: 20037508.342789244" << std::endl;
    
    return 0;
}
