/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */

/**
 * THE DEMO APPLICATION is an ImGui-based app that shows off all the features
 * of the Rocky Application API. We intend each "Demo_*" include file to be
 * both a unit test for that feature, and a reference or writing your own code.
 */

#include <rocky/rocky.h>
#include <rocky/vsg/imgui/ImGuiIntegration.h>
#include <rocky/Config.h>
#include <filesystem>
#include "TileValidator.h"
#include "ElevationValidator.h"

using namespace ROCKY_NAMESPACE;

// 全局高程缩放配置 - 确保着色器和包围球计算使用相同的值
namespace ElevationScaling
{
    constexpr float ELEVATION_SCALE = 0.05f; // 地球尺度的高程缩放因子 (5%)

    // 获取当前高程缩放因子
    inline float getElevationScale()
    {
        return ELEVATION_SCALE;
    }

    // 说明：
    // - 对于地球系统，高程值需要适当缩放以避免过度夸张
    // - 地球半径约6378公里，高程通常在-500到8848米之间
    // - 使用0.05的缩放因子可以得到明显的地形起伏效果
    // - 这个值必须在着色器和包围球计算中保持一致
}

#include "Demo_Map.h"
#include "Demo_Line.h"
#include "Demo_Mesh.h"
#include "Demo_Icon.h"
#include "Demo_Model.h"
#include "Demo_Label.h"
#include "Demo_Widget.h"
#include "Demo_LineFeatures.h"
#include "Demo_PolygonFeatures.h"
#include "Demo_LabelFeatures.h"
#include "Demo_GeosGeometry.h"
#include "Demo_MapManipulator.h"
#include "Demo_Serialization.h"
#include "Demo_Tethering.h"
#include "Demo_Environment.h"
#include "Demo_Views.h"
#include "Demo_RTT.h"
#include "Demo_Stats.h"
#include "Demo_Geocoder.h"
#include "Demo_Rendering.h"
#include "Demo_Simulation.h"
#include "Demo_TrackHistory.h"
#include "Demo_Decluttering.h"
#include "Demo_Registry.h"
#include "Demo_NodePager.h"

template <class T>
int layerError(T layer)
{
    rocky::Log()->warn("Problem with layer \"" + layer->name() + "\" : " + layer->status().message);
    return -1;
}

auto Demo_About = [](Application &app)
{
    for (auto &about : rocky::ContextImpl::about())
    {
        ImGui::Text(about.c_str());
    }
};

auto Demo_TileValidation = [](Application &app)
{
    ImGui::Text("瓦片验证和数据分析工具");
    ImGui::Separator();

    if (ImGui::Button("生成瓦片对应关系报告"))
    {
        if (auto validator = TileValidation::getValidator())
        {
            validator->generateFullReport();
            rocky::Log()->info("瓦片验证报告已生成，请查看 cache/analysis/ 目录");
        }
    }

    ImGui::SameLine();
    if (ImGui::Button("打开缓存目录"))
    {
        std::string command = "explorer cache";
        system(command.c_str());
    }

    ImGui::Separator();
    ImGui::Text("地形高程缩放控制:");

    float current_scale = ElevationScaling::getElevationScale();
    ImGui::Text("当前高程缩放因子: %.4f", current_scale);
    ImGui::Text("地形高度缩放比例: %.1f%%", current_scale * 100000);

    if (ImGui::IsItemHovered())
    {
        ImGui::SetTooltip("当前使用固定的高程缩放因子\n"
                          "0.05 = 明显地形起伏 (当前设置)\n"
                          "这确保了着色器和包围球计算的一致性");
    }

    ImGui::Text("缩放效果:");
    ImGui::BulletText("珠穆朗玛峰 (8848m) → 显示高度: %.0fm", 8848.0f * current_scale);
    ImGui::BulletText("马里亚纳海沟 (-11034m) → 显示深度: %.0fm", -11034.0f * current_scale);
    ImGui::BulletText("地形起伏明显，可以清楚看到山脉和峡谷");

    if (ImGui::Button("查看高程缩放说明"))
    {
        ImGui::OpenPopup("高程缩放说明");
    }

    if (ImGui::BeginPopup("高程缩放说明"))
    {
        ImGui::Text("高程缩放技术说明:");
        ImGui::Separator();
        ImGui::Text("• 地球半径: ~6378公里");
        ImGui::Text("• 最高点: 珠穆朗玛峰 8848米");
        ImGui::Text("• 最低点: 马里亚纳海沟 -11034米");
        ImGui::Text("• 缩放因子: 0.05 (5%%)");
        ImGui::Text("• 目的: 在地球尺度下显示明显的地形起伏");
        ImGui::Text("• 一致性: 着色器和包围球计算使用相同值");
        ImGui::EndPopup();
    }

    ImGui::Separator();
    ImGui::Text("验证功能:");
    ImGui::BulletText("检查影像瓦片和高程瓦片的对应关系");
    ImGui::BulletText("验证AWS Terrarium高程数据的正确性");
    ImGui::BulletText("分析瓦片的地理坐标和数据质量");
    ImGui::BulletText("生成详细的验证报告");

    ImGui::Text("缓存目录结构:");
    ImGui::BulletText("cache/imagery/ - 影像瓦片缓存");
    ImGui::BulletText("cache/elevation/ - 高程瓦片缓存");
    ImGui::BulletText("cache/analysis/ - 验证分析报告");
};

struct Demo
{
    std::string name;
    std::function<void(Application &)> function;
    std::vector<Demo> children;
};

std::vector<Demo> demos =
    {
        Demo{"Map", Demo_Map},
        Demo{"Basic Components", {}, {
                                         Demo{"Label", Demo_Label}, Demo{"Line - absolute", Demo_Line_Absolute}, Demo{"Line - relative", Demo_Line_Relative}, Demo{"Mesh - absolute", Demo_Mesh_Absolute}, Demo{"Mesh - relative", Demo_Mesh_Relative}, Demo{"Icon", Demo_Icon}, Demo{"Model", Demo_Model}, Demo{"Widget", Demo_Widget}
                                         // Demo{ "Registry", Demo_Registry }
                                     }},
        Demo{"GIS Data", {}, {Demo{"GEOS Geometry", Demo_GeosGeometry}, Demo{"Polygon features", Demo_PolygonFeatures}, Demo{"Line features", Demo_LineFeatures}, Demo{"Labels from features", Demo_LabelFeatures}}}, Demo{"Simulation", {}, {Demo{"Simulated platforms", Demo_Simulation}, Demo{"Track histories", Demo_TrackHistory}}}, Demo{"Decluttering", Demo_Decluttering}, Demo{"Node Pager", Demo_NodePager}, Demo{"Geocoding", Demo_Geocoder}, Demo{"RTT", Demo_RTT}, Demo{"Camera", {}, {Demo{"Viewpoints", Demo_Viewpoints}, Demo{"Tethering", Demo_Tethering}}}, Demo{"Rendering", Demo_Rendering}, Demo{"Views", Demo_Views}, Demo{"Environment", Demo_Environment}, Demo{"Serialization", Demo_Serialization}, Demo{"Stats", Demo_Stats}, Demo{"Tile Validation", Demo_TileValidation}, Demo{"About", Demo_About}};

struct MainGUI : public vsg::Inherit<ImGuiNode, MainGUI>
{
    Application &app;
    MainGUI(Application &app_) : app(app_) {}
    mutable ImVec2 asize;
    mutable std::optional<std::string> attribution;

    void render(ImGuiContext *imguiContext) const override
    {
        ImGui::SetCurrentContext(imguiContext);
        ImGui::Begin("Welcome to Rocky");
        {
            for (auto &demo : demos)
            {
                render(demo);
            }
        }
        ImGui::End();

        renderAttribution();
    }

    void render(const Demo &demo) const
    {
        if (ImGui::CollapsingHeader(demo.name.c_str()))
        {
            if (demo.function)
                demo.function(app);

            if (!demo.children.empty())
            {
                ImGui::Indent();
                for (auto &child : demo.children)
                    render(child);
                ImGui::Unindent();
            }
        }
    }

    void renderAttribution() const
    {
        // Map attribution
        if (!attribution.has_value())
        {
            std::string buf;
            const auto &layers = app.mapNode->map->layers().all();
            for (const auto &layer : layers)
            {
                if (layer->status().ok() && layer->attribution.has_value())
                {
                    if (!buf.empty())
                        buf += ", ";
                    buf += layer->attribution->text;
                }
            }
            attribution = buf;
        }

        if (!attribution->empty())
        {
            auto winsize = ImGui::GetIO().DisplaySize;
            ImGui::SetNextWindowPos(ImVec2(winsize.x - asize.x, winsize.y - asize.y));
            ImGui::SetNextWindowBgAlpha(0.65f);
            ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
            ImGui::Begin("##Attribution", nullptr,
                         ImGuiWindowFlags_NoDecoration | ImGuiWindowFlags_AlwaysAutoResize |
                             ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoMove);
            ImGui::Text("%s", attribution->c_str());
            asize = ImGui::GetWindowSize();
            ImGui::End();
            ImGui::PopStyleVar(1);
        }
    }
};

int main(int argc, char **argv)
{
    // 设置控制台UTF-8编码，防止中文乱码
    system("chcp 65001");

    // 初始化全局配置
    Log()->info("Starting Rocky Demo...");
    if (!GlobalConfig::instance().initialize())
    {
        Log()->warn("配置初始化失败，将使用默认设置");
    }

    // Set the PROJ_DATA environment variable to point to our local data directory.
    // This MUST be done before any rocky operation that might initialize PROJ.
    std::filesystem::path exe_path = rocky::util::getExecutableLocation();
    std::filesystem::path proj_path = exe_path.parent_path() / "share" / "proj";
    _putenv_s("PROJ_DATA", proj_path.string().c_str());

    // instantiate the application engine.
    rocky::Application app(argc, argv);

    // 设置应用程序的代理配置
    auto proxySettings = GlobalConfig::instance().getProxySettings();
    if (proxySettings.has_value())
    {
        app.io().proxySettings = proxySettings;
        Log()->info("应用程序代理配置已设置: {}:{} ({})",
                    proxySettings->host, proxySettings->port,
                    proxySettings->enabled ? "启用" : "禁用");
    }

    // 配置地形设置以解决瓦片缝隙问题
    app.mapNode->terrainSettings().screenSpaceError = 1.75f; // 地形质量
    app.mapNode->terrainSettings().skirtRatio = 0.05f;       // 添加地形裙边以消除缝隙

    // Exit if the user tries to load a file and failed:
    if (app.commandLineStatus.failed())
    {
        Log()->error(app.commandLineStatus.toString());
        exit(-1);
    }

    // Add some default layers if the user didn't load a file:
    auto &layers = app.mapNode->map->layers();
    if (layers.empty())
    {
        // Google Maps satellite imagery
        auto imagery = rocky::TMSImageLayer::create();
        imagery->uri = "http://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
        imagery->profile = rocky::Profile("spherical-mercator");
        imagery->attribution = rocky::Hyperlink{"\u00a9 Google", "https://google.com/copyright"};
        imagery->setName("Google Maps Satellite");
        layers.add(imagery);

        // 创建缓存目录用于验证瓦片对应关系
        std::string cacheDir = "cache";
        std::filesystem::create_directories(cacheDir);
        std::filesystem::create_directories(cacheDir + "/imagery");
        std::filesystem::create_directories(cacheDir + "/elevation");
        Log()->info("Cache directory created: {}", cacheDir);

        // 初始化瓦片验证器
        TileValidation::initializeValidator(cacheDir);
        Log()->info("Tile validator initialized for data validation");

        // 初始化高程验证器
        ElevationValidation::initializeElevationValidator(cacheDir);
        Log()->info("Elevation validator initialized for ReadyMap data validation");

        // 瓦片缓存功能已集成到TMS下载流程中
        Log()->info("Tile caching enabled - tiles will be automatically saved to cache directory");

        // 使用AWS Terrarium高程数据源 (PNG格式)
        auto elevation = rocky::TMSElevationLayer::create();
        // elevation->uri = "https://readymap.org/readymap/tiles/1.0.0/116/";
        elevation->uri = "https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png";
        elevation->profile = rocky::Profile("spherical-mercator");
        elevation->encoding = rocky::ElevationLayer::Encoding::TerrariumRGB; // 设置正确的编码
        elevation->attribution = rocky::Hyperlink{"\u00a9 AWS Terrarium", "https://registry.opendata.aws/terrain-tiles/"};
        elevation->setName("AWS Terrarium Elevation");
        Log()->info("使用AWS Terrarium PNG高程数据 (TerrariumRGB编码)");
        Log()->info("Imagery URL: {}", imagery->uri.value().full());
        Log()->info("Elevation URL: {}", elevation->uri.value().full());
        Log()->info("Elevation Encoding: TerrariumRGB");
        Log()->info("HTTPS support check: {}", rocky::URI::supportsHTTPS() ? "ENABLED" : "DISABLED");
        Log()->info("AWS Terrarium HTTPS elevation configured - testing 3D terrain and data validation!");
        layers.add(elevation);
    }

    // Create the main window and the main GUI:
    auto traits = vsg::WindowTraits::create(1920, 1080, "Main Window");
    auto main_window = app.displayManager->addWindow(traits);

    // Install a manager for our main GUI:
    auto imgui_group = ImGuiIntegration::addContextGroup(app.displayManager, main_window);

    // Hook in the main demo gui:
    auto demo_gui = MainGUI::create(app);
    imgui_group->add(demo_gui, app);

    // run until the user quits.
    return app.run();
}
