﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{DA118014-FAC1-33E8-B89B-766EAA796615}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>rocky</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">rocky.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">rocky</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">rocky.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">rocky</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">rocky.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">rocky</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">rocky.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">rocky</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.dll</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;ROCKY_BUILDING_SDK;ROCKY_BUILDING_SHARED_LIBRARY;VSGXCHANGE_SHARED_LIBRARY;VSG_SHARED_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR="Debug";rocky_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;ROCKY_BUILDING_SDK;ROCKY_BUILDING_SHARED_LIBRARY;VSGXCHANGE_SHARED_LIBRARY;VSG_SHARED_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR=\"Debug\";rocky_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/Debug/rocky.dll -installedDir C:/dev/vcpkg/installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\debug\lib\proj_d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\libcurl-d.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\libssl.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\libcrypto.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\zlibd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\imguid.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\vsgXchanged.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\glm.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\spdlogd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\vsgd.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\vulkan-1.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\fmtd.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/Debug/rocky.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/Debug/rocky.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;ROCKY_BUILDING_SHARED_LIBRARY;VSGXCHANGE_SHARED_LIBRARY;VSG_SHARED_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR="Release";rocky_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;ROCKY_BUILDING_SHARED_LIBRARY;VSGXCHANGE_SHARED_LIBRARY;VSG_SHARED_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR=\"Release\";rocky_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/Release/rocky.dll -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\lib\proj.lib;C:\dev\vcpkg\installed\x64-windows\lib\libcurl.lib;C:\dev\vcpkg\installed\x64-windows\lib\libssl.lib;C:\dev\vcpkg\installed\x64-windows\lib\libcrypto.lib;C:\dev\vcpkg\installed\x64-windows\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\lib\zlib.lib;C:\dev\vcpkg\installed\x64-windows\lib\imgui.lib;C:\dev\vcpkg\installed\x64-windows\lib\vsgXchange.lib;C:\dev\vcpkg\installed\x64-windows\lib\glm.lib;C:\dev\vcpkg\installed\x64-windows\lib\spdlog.lib;C:\dev\vcpkg\installed\x64-windows\lib\vsg.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\vulkan-1.lib;C:\dev\vcpkg\installed\x64-windows\lib\fmt.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/Release/rocky.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/Release/rocky.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;ROCKY_BUILDING_SHARED_LIBRARY;VSGXCHANGE_SHARED_LIBRARY;VSG_SHARED_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR="MinSizeRel";rocky_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;ROCKY_BUILDING_SHARED_LIBRARY;VSGXCHANGE_SHARED_LIBRARY;VSG_SHARED_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR=\"MinSizeRel\";rocky_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/MinSizeRel/rocky.dll -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\lib\proj.lib;C:\dev\vcpkg\installed\x64-windows\lib\libcurl.lib;C:\dev\vcpkg\installed\x64-windows\lib\libssl.lib;C:\dev\vcpkg\installed\x64-windows\lib\libcrypto.lib;C:\dev\vcpkg\installed\x64-windows\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\lib\zlib.lib;C:\dev\vcpkg\installed\x64-windows\lib\imgui.lib;C:\dev\vcpkg\installed\x64-windows\lib\vsgXchange.lib;C:\dev\vcpkg\installed\x64-windows\lib\glm.lib;C:\dev\vcpkg\installed\x64-windows\lib\spdlog.lib;C:\dev\vcpkg\installed\x64-windows\lib\vsg.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\vulkan-1.lib;C:\dev\vcpkg\installed\x64-windows\lib\fmt.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/MinSizeRel/rocky.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/MinSizeRel/rocky.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/dev/vcpkg/installed/x64-windows/include" /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;ROCKY_BUILDING_SHARED_LIBRARY;VSGXCHANGE_SHARED_LIBRARY;VSG_SHARED_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR="RelWithDebInfo";rocky_EXPORTS</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;ROCKY_BUILDING_SDK;ROCKY_BUILDING_SHARED_LIBRARY;VSGXCHANGE_SHARED_LIBRARY;VSG_SHARED_LIBRARY;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CMAKE_INTDIR=\"RelWithDebInfo\";rocky_EXPORTS</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include;F:\cmo-dev\my_osgearth_web\rocky\src;F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky;C:\dev\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
"C:\Program Files\PowerShell\7\pwsh.exe" -noprofile -executionpolicy Bypass -file C:/dev/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/RelWithDebInfo/rocky.dll -installedDir C:/dev/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\dev\vcpkg\installed\x64-windows\lib\proj.lib;C:\dev\vcpkg\installed\x64-windows\lib\libcurl.lib;C:\dev\vcpkg\installed\x64-windows\lib\libssl.lib;C:\dev\vcpkg\installed\x64-windows\lib\libcrypto.lib;C:\dev\vcpkg\installed\x64-windows\lib\sqlite3.lib;C:\dev\vcpkg\installed\x64-windows\lib\zlib.lib;C:\dev\vcpkg\installed\x64-windows\lib\imgui.lib;C:\dev\vcpkg\installed\x64-windows\lib\vsgXchange.lib;C:\dev\vcpkg\installed\x64-windows\lib\glm.lib;C:\dev\vcpkg\installed\x64-windows\lib\spdlog.lib;C:\dev\vcpkg\installed\x64-windows\lib\vsg.lib;C:\dev\vcpkg\installed\x64-windows\debug\lib\vulkan-1.lib;C:\dev\vcpkg\installed\x64-windows\lib\fmt.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/RelWithDebInfo/rocky.lib</ImportLibrary>
      <ProgramDataBaseFile>F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/RelWithDebInfo/rocky.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/rocky/src/rocky/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSSL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgMacros.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Version.h.in;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rocky-config.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/rocky/src/rocky/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSSL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgMacros.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Version.h.in;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rocky-config.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/rocky/src/rocky/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSSL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgMacros.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Version.h.in;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rocky-config.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule F:/cmo-dev/my_osgearth_web/rocky/src/rocky/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SF:/cmo-dev/my_osgearth_web/rocky -BF:/cmo-dev/my_osgearth_web/rocky/build_desk --check-stamp-file F:/cmo-dev/my_osgearth_web/rocky/build_desk/src/rocky/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.26\Modules\BasicConfigVersion-AnyNewerVersion.cmake.in;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CMakePackageConfigHelpers.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindOpenSSL.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindTIFF.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\FindZLIB.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\SelectLibraryConfigurations.cmake;C:\Program Files\CMake\share\cmake-3.26\Modules\WriteBasicConfigVersionFile.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\CURLTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\curl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\entt\EnTTTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\glm\glmConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\openssl\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-config-version.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\proj\proj4-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\tiff\vcpkg-cmake-wrapper.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-config.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\unofficial-sqlite3\unofficial-sqlite3-targets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgMacros.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsg\vsgTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfig.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeConfigVersion.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-debug.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets-release.cmake;C:\dev\vcpkg\installed\x64-windows\share\vsgxchange\vsgXchangeTargets.cmake;C:\dev\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;F:\cmo-dev\my_osgearth_web\rocky\cmake\FindImGui.cmake;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Version.h.in;F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rocky-config.cmake.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Azure.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\AzureImageLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Bing.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\BingElevationLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\BingImageLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Callbacks.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Color.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Common.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Context.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\DateTime.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\ElevationLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Ellipsoid.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Ephemeris.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Feature.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoCircle.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoCommon.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoExtent.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoHeightfield.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoImage.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoPoint.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Geocoder.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Geoid.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Heightfield.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Horizon.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\IOTypes.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Image.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\ImageLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\LRUCache.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Layer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\LayerCollection.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\LayerReference.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Log.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTiles.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTilesElevationLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTilesImageLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Map.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Math.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Memory.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Profile.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\SRS.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\SentryTracker.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Status.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMS.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMSElevationLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMSImageLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TerrainTileModel.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TerrainTileModelFactory.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Threading.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TileKey.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TileLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\URI.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Units.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Utils.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Viewpoint.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\VisibleLayer.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\json.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\option.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rocky.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\rtree.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\sha1.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\tinyxml\tinyxml.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\weejobs.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\weemesh.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Version.h.in" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\build_desk\build_include\rocky\Version.h" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Azure.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\AzureImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\BingElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\BingImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Color.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Context.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\DateTime.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\ElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Ellipsoid.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Ephemeris.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Feature.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoCircle.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoExtent.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoHeightfield.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoImage.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\GeoPoint.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Geocoder.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Geoid.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Heightfield.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Horizon.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\IOTypes.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Image.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\ImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Layer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\LayerCollection.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Log.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTiles.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTilesElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\MBTilesImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Map.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Math.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Memory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Profile.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\SRS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Status.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMS.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMSElevationLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TMSImageLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TerrainTileModel.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TerrainTileModelFactory.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Threading.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TileKey.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\TileLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\URI.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Units.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Utils.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\Viewpoint.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\VisibleLayer.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\static.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\tinyxml\tinyxml.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\tinyxml\tinyxmlerror.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\tinyxml\tinyxmlparser.cpp" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\contrib\EarthFileImporter.h" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\contrib\EarthFileImporter.cpp" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\Application.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\Common.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\DisplayManager.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\GeoTransform.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\MapManipulator.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\MapNode.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\NodePager.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\PipelineState.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\PixelScaleTransform.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\RTT.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\SkyNode.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\Utils.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\VSGContext.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ViewLocal.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\json.h" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\Application.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\DisplayManager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\GeoTransform.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\MapManipulator.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\MapNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\NodePager.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\PipelineState.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\RTT.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\SkyNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\VSGContext.cpp" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\GeometryPool.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\SurfaceNode.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainEngine.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainNode.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainSettings.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainState.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainTileHost.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainTileNode.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainTilePager.h" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\GeometryPool.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\SurfaceNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainEngine.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainSettings.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainState.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainTileNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\terrain\TerrainTilePager.cpp" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Component.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Declutter.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\ECSNode.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\FeatureView.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Icon.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\IconSystem.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\IconSystem2.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Label.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\LabelSystem.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Line.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\LineSystem.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Mesh.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\MeshSystem.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Motion.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\MotionSystem.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Registry.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Transform.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\TransformDetail.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\TransformSystem.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Visibility.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Widget.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\WidgetSystem.h" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\ECSNode.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\FeatureView.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\IconSystem.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\IconSystem2.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\LabelSystem.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\LineSystem.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\MeshSystem.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\Registry.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\TransformDetail.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\TransformSystem.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\ecs\WidgetSystem.cpp" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\ImGuiIntegration.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\RenderImGui.h" />
    <ClInclude Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\SendEventsToImGui.h" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\ImGuiIntegration.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\RenderImGui.cpp" />
    <ClCompile Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\imgui\SendEventsToImGui.cpp" />
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.atmo.ground.vert.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.atmo.sky.frag">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.atmo.sky.vert">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.icon.frag">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.icon.indirect.cull.comp">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.icon.indirect.frag">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.icon.indirect.vert">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.icon.vert">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.lighting.frag.glsl">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.line.frag">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.line.vert">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.mesh.frag">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.mesh.vert">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.terrain.frag">
    </None>
    <None Include="F:\cmo-dev\my_osgearth_web\rocky\src\rocky\vsg\shaders\rocky.terrain.vert">
    </None>
    <Natvis Include="C:\dev\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_json.natvis">
    </Natvis>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="F:\cmo-dev\my_osgearth_web\rocky\build_desk\ZERO_CHECK.vcxproj">
      <Project>{78E83F7C-5EE9-3106-A2D4-B7FFC6B536F1}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>