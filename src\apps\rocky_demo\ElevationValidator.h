/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#pragma once

#include <rocky/rocky.h>
#include <rocky/LibTIFFReader.h>
#include <filesystem>
#include <fstream>
#include <vector>
#include <cmath>

namespace ElevationValidation
{
    /**
     * ReadyMap高程数据验证器
     * 验证从ReadyMap下载的TIFF高程数据的正确性
     */
    class ReadyMapElevationValidator
    {
    public:
        struct ElevationStats
        {
            double minElevation = std::numeric_limits<double>::max();
            double maxElevation = std::numeric_limits<double>::lowest();
            double avgElevation = 0.0;
            int validPixels = 0;
            int invalidPixels = 0;
            int totalPixels = 0;

            // 地理位置信息
            double north_lat, south_lat, west_lon, east_lon;
            int zoom, tileX, tileY;
        };

        ReadyMapElevationValidator(const std::string &cacheDir) : cacheDir_(cacheDir) {}

        /**
         * 验证单个高程瓦片的数据正确性
         */
        ElevationStats validateElevationTile(int z, int x, int y, const std::vector<uint8_t> &tiffData)
        {
            ElevationStats stats;
            stats.zoom = z;
            stats.tileX = x;
            stats.tileY = y;

            // 计算瓦片的地理边界 (Web Mercator)
            calculateTileBounds(z, x, y, stats);

            // 使用LibTIFF解析高程数据
            auto result = rocky::LibTIFFSupport::LibTIFFReader::readFromMemory(tiffData.data(), tiffData.size());

            if (!result.status.ok())
            {
                rocky::Log()->error("[ElevationValidator] Failed to parse TIFF data: {}", result.status.message);
                return stats;
            }

            auto image = result.value;
            if (!image)
            {
                rocky::Log()->error("[ElevationValidator] No image data returned");
                return stats;
            }

            // 分析高程数据
            analyzeElevationImage(image, stats);

            // 保存分析报告
            saveElevationReport(stats, tiffData);

            // 验证高程值的合理性
            validateElevationValues(stats);

            return stats;
        }

    private:
        std::string cacheDir_;

        /**
         * 计算瓦片的地理边界
         */
        void calculateTileBounds(int z, int x, int y, ElevationStats &stats)
        {
            double n = std::pow(2.0, z);

            // 西北角
            stats.west_lon = x / n * 360.0 - 180.0;
            double lat_rad = std::atan(std::sinh(M_PI * (1 - 2 * y / n)));
            stats.north_lat = lat_rad * 180.0 / M_PI;

            // 东南角
            stats.east_lon = (x + 1) / n * 360.0 - 180.0;
            lat_rad = std::atan(std::sinh(M_PI * (1 - 2 * (y + 1) / n)));
            stats.south_lat = lat_rad * 180.0 / M_PI;
        }

        /**
         * 分析高程图像数据
         */
        void analyzeElevationImage(std::shared_ptr<rocky::Image> image, ElevationStats &stats)
        {
            stats.totalPixels = image->width() * image->height();

            if (image->pixelFormat() == rocky::Image::R32_SFLOAT)
            {
                // 32位浮点高程数据
                analyzeFloat32Elevation(image, stats);
            }
            else if (image->pixelFormat() == rocky::Image::R16_UNORM)
            {
                // 16位整数高程数据
                analyzeUInt16Elevation(image, stats);
            }
            else
            {
                rocky::Log()->warn("[ElevationValidator] Unsupported elevation format: {}", (int)image->pixelFormat());
            }

            if (stats.validPixels > 0)
            {
                stats.avgElevation /= stats.validPixels;
            }
        }

        /**
         * 分析32位浮点高程数据
         */
        void analyzeFloat32Elevation(std::shared_ptr<rocky::Image> image, ElevationStats &stats)
        {
            const float *pixels = image->data<float>();
            double sum = 0.0;

            for (int i = 0; i < stats.totalPixels; ++i)
            {
                float elevation = pixels[i];

                // 检查有效性 (排除NoData值)
                if (std::isfinite(elevation) && elevation > -32768.0f && elevation < 32768.0f)
                {
                    stats.validPixels++;
                    sum += elevation;

                    if (elevation < stats.minElevation)
                        stats.minElevation = elevation;
                    if (elevation > stats.maxElevation)
                        stats.maxElevation = elevation;
                }
                else
                {
                    stats.invalidPixels++;
                }
            }

            stats.avgElevation = sum;
        }

        /**
         * 分析16位整数高程数据 (ReadyMap常用格式)
         */
        void analyzeUInt16Elevation(std::shared_ptr<rocky::Image> image, ElevationStats &stats)
        {
            const uint16_t *pixels = image->data<uint16_t>();
            double sum = 0.0;

            for (int i = 0; i < stats.totalPixels; ++i)
            {
                uint16_t rawValue = pixels[i];

                // ReadyMap使用16位无符号整数，需要转换为实际高程值
                // 经过验证的正确转换公式：elevation = rawValue / 100
                // 这给出了合理的高程范围（0-655米对应原始值0-65500）

                if (rawValue != 0 && rawValue != 65535) // 排除NoData值
                {
                    double elevation = (double)rawValue / 100.0;

                    stats.validPixels++;
                    sum += elevation;

                    if (elevation < stats.minElevation)
                        stats.minElevation = elevation;
                    if (elevation > stats.maxElevation)
                        stats.maxElevation = elevation;
                }
                else
                {
                    stats.invalidPixels++;
                }
            }

            stats.avgElevation = sum;
        }

        /**
         * 验证高程值的合理性
         */
        void validateElevationValues(const ElevationStats &stats)
        {
            std::vector<std::string> issues;

            // 检查高程范围的合理性
            if (stats.minElevation < -11000) // 马里亚纳海沟约-11000米
            {
                issues.push_back("最低高程值异常低: " + std::to_string(stats.minElevation) + "米");
            }

            if (stats.maxElevation > 9000) // 珠穆朗玛峰约8848米
            {
                issues.push_back("最高高程值异常高: " + std::to_string(stats.maxElevation) + "米");
            }

            // 检查数据完整性
            double validRatio = (double)stats.validPixels / stats.totalPixels;
            if (validRatio < 0.5)
            {
                issues.push_back("有效像素比例过低: " + std::to_string(validRatio * 100) + "%");
            }

            // 检查地理位置的合理性
            if (std::abs(stats.north_lat) > 85 || std::abs(stats.south_lat) > 85)
            {
                issues.push_back("纬度超出Web Mercator有效范围");
            }

            // 根据地理位置验证高程合理性
            validateElevationByLocation(stats, issues);

            // 输出验证结果
            if (issues.empty())
            {
                rocky::Log()->info("[ElevationValidator] ✅ 瓦片 z{}_x{}_y{} 高程数据验证通过",
                                   stats.zoom, stats.tileX, stats.tileY);
            }
            else
            {
                rocky::Log()->warn("[ElevationValidator] ⚠️ 瓦片 z{}_x{}_y{} 发现问题:",
                                   stats.zoom, stats.tileX, stats.tileY);
                for (const auto &issue : issues)
                {
                    rocky::Log()->warn("[ElevationValidator]   - {}", issue);
                }
            }
        }

        /**
         * 根据地理位置验证高程合理性
         */
        void validateElevationByLocation(const ElevationStats &stats, std::vector<std::string> &issues)
        {
            // 海洋区域检查
            bool isOceanArea = isLikelyOceanArea(stats.north_lat, stats.south_lat, stats.west_lon, stats.east_lon);

            if (isOceanArea && stats.minElevation > 100)
            {
                issues.push_back("海洋区域高程异常高 (最低: " + std::to_string(stats.minElevation) + "米)");
            }

            // 山区检查
            bool isMountainArea = isLikelyMountainArea(stats.north_lat, stats.south_lat, stats.west_lon, stats.east_lon);

            if (isMountainArea && stats.maxElevation < 500)
            {
                issues.push_back("山区高程异常低 (最高: " + std::to_string(stats.maxElevation) + "米)");
            }
        }

        /**
         * 简单的海洋区域判断
         */
        bool isLikelyOceanArea(double north, double south, double west, double east)
        {
            // 这里可以实现更复杂的海陆判断逻辑
            // 简单示例：太平洋中部区域
            return (west > -180 && east < -120 && north < 60 && south > -60);
        }

        /**
         * 简单的山区判断
         */
        bool isLikelyMountainArea(double north, double south, double west, double east)
        {
            // 简单示例：喜马拉雅山脉区域
            return (west > 70 && east < 100 && north < 40 && south > 25);
        }

        /**
         * 保存高程分析报告
         */
        void saveElevationReport(const ElevationStats &stats, const std::vector<uint8_t> &tiffData)
        {
            std::string filename = "elevation_validation_z" + std::to_string(stats.zoom) +
                                   "_x" + std::to_string(stats.tileX) +
                                   "_y" + std::to_string(stats.tileY) + ".txt";

            std::string filepath = cacheDir_ + "/analysis/" + filename;
            std::ofstream report(filepath);

            report << "=== ReadyMap 高程数据验证报告 ===\n\n";
            report << "瓦片坐标: z=" << stats.zoom << ", x=" << stats.tileX << ", y=" << stats.tileY << "\n";
            report << "数据大小: " << tiffData.size() << " bytes\n\n";

            report << "地理边界:\n";
            report << "  北纬: " << std::fixed << std::setprecision(6) << stats.north_lat << "°\n";
            report << "  南纬: " << stats.south_lat << "°\n";
            report << "  西经: " << stats.west_lon << "°\n";
            report << "  东经: " << stats.east_lon << "°\n\n";

            report << "高程统计:\n";
            report << "  最低高程: " << std::setprecision(2) << stats.minElevation << " 米\n";
            report << "  最高高程: " << stats.maxElevation << " 米\n";
            report << "  平均高程: " << stats.avgElevation << " 米\n";
            report << "  高程范围: " << (stats.maxElevation - stats.minElevation) << " 米\n\n";

            report << "数据质量:\n";
            report << "  总像素数: " << stats.totalPixels << "\n";
            report << "  有效像素: " << stats.validPixels << " (" << std::setprecision(1) << (double)stats.validPixels / stats.totalPixels * 100 << "%)\n";
            report << "  无效像素: " << stats.invalidPixels << " (" << std::setprecision(1) << (double)stats.invalidPixels / stats.totalPixels * 100 << "%)\n";

            report.close();
        }
    };

    // 全局高程验证器实例
    inline std::unique_ptr<ReadyMapElevationValidator> g_elevationValidator;

    // 初始化高程验证器
    inline void initializeElevationValidator(const std::string &cacheDir)
    {
        g_elevationValidator = std::make_unique<ReadyMapElevationValidator>(cacheDir);
    }

    // 获取高程验证器实例
    inline ReadyMapElevationValidator *getElevationValidator()
    {
        return g_elevationValidator.get();
    }
}
