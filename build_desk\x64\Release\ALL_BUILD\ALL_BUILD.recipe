﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\rocky\build_desk\x64\Release\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\rocky\Release\rocky.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_demo\Release\rocky_demo.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_engine\Release\rocky_engine.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\rocky_simple\Release\rocky_simple.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\apps\Release\rocky_srs_test.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\rocky\build_desk\src\tests\Release\rocky_tests.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>F:\cmo-dev\my_osgearth_web\rocky\build_desk\x64\Release\ALL_BUILD</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>