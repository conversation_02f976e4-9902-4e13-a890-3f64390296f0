#include <rocky/SRS.h>
#include <rocky/GeoPoint.h>
#include <rocky/Log.h>
#include <iostream>

using namespace rocky;

int main()
{
    // 初始化日志 (Log::init()在新版本中可能不存在)
    // Log::init();

    std::cout << "=== Rocky SRS 重构测试 ===" << std::endl;

    // 测试1: 创建WGS84坐标系
    std::cout << "\n1. 测试WGS84坐标系创建:" << std::endl;
    SRS wgs84("wgs84");
    if (wgs84.valid())
    {
        std::cout << "   ✓ WGS84坐标系创建成功" << std::endl;
        std::cout << "   名称: " << wgs84.name() << std::endl;
        std::cout << "   是否为地理坐标系: " << (wgs84.isGeodetic() ? "是" : "否") << std::endl;
        std::cout << "   版本信息: " << SRS::projVersion() << std::endl;
    }
    else
    {
        std::cout << "   ✗ WGS84坐标系创建失败" << std::endl;
        return 1;
    }

    // 测试2: 创建Web Mercator坐标系
    std::cout << "\n2. 测试Web Mercator坐标系创建:" << std::endl;
    SRS webMercator("spherical-mercator");
    if (webMercator.valid())
    {
        std::cout << "   ✓ Web Mercator坐标系创建成功" << std::endl;
        std::cout << "   名称: " << webMercator.name() << std::endl;
        std::cout << "   是否为投影坐标系: " << (webMercator.isProjected() ? "是" : "否") << std::endl;
    }
    else
    {
        std::cout << "   ✗ Web Mercator坐标系创建失败" << std::endl;
        return 1;
    }

    // 测试3: 测试EPSG代码
    std::cout << "\n3. 测试EPSG代码:" << std::endl;
    SRS epsg4326("epsg:4326");
    SRS epsg3857("epsg:3857");

    if (epsg4326.valid() && epsg3857.valid())
    {
        std::cout << "   ✓ EPSG代码解析成功" << std::endl;
        std::cout << "   EPSG:4326 等价于 WGS84: " << (epsg4326.equivalentTo(wgs84) ? "是" : "否") << std::endl;
        std::cout << "   EPSG:3857 等价于 Web Mercator: " << (epsg3857.equivalentTo(webMercator) ? "是" : "否") << std::endl;
    }
    else
    {
        std::cout << "   ✗ EPSG代码解析失败" << std::endl;
    }

    // 测试4: 坐标转换
    std::cout << "\n4. 测试坐标转换:" << std::endl;
    try
    {
        // 创建一个北京的地理坐标点 (116.4074, 39.9042)
        GeoPoint beijing(wgs84, 116.4074, 39.9042, 0.0);
        std::cout << "   原始坐标 (WGS84): " << beijing.x << ", " << beijing.y << std::endl;

        // 转换到Web Mercator
        GeoPoint beijingMercator = beijing.transform(webMercator);
        if (beijingMercator.srs.valid())
        {
            std::cout << "   ✓ 坐标转换成功" << std::endl;
            std::cout << "   Web Mercator坐标: " << beijingMercator.x << ", " << beijingMercator.y << std::endl;
        }
        else
        {
            std::cout << "   ✗ 坐标转换失败" << std::endl;
        }
    }
    catch (const std::exception &e)
    {
        std::cout << "   ✗ 坐标转换异常: " << e.what() << std::endl;
    }

    // 测试5: 椭球体参数
    std::cout << "\n5. 测试椭球体参数:" << std::endl;
    const Ellipsoid &ellipsoid = wgs84.ellipsoid();
    std::cout << "   WGS84椭球体长半轴: " << ellipsoid.semiMajorAxis() << " 米" << std::endl;
    std::cout << "   WGS84椭球体短半轴: " << ellipsoid.semiMinorAxis() << " 米" << std::endl;
    // std::cout << "   WGS84椭球体扁率: " << ellipsoid.flattening() << std::endl;

    std::cout << "\n=== 测试完成 ===" << std::endl;
    std::cout << "Rocky SRS重构成功！从PROJ库迁移到GeographicLib适配器完成。" << std::endl;

    return 0;
}
