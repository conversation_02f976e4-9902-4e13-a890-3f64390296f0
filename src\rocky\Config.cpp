/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#include "Config.h"
#include "Log.h"
#include <fstream>
#include <sstream>

using namespace ROCKY_NAMESPACE;

bool Config::loadFromFile(const std::string& filename)
{
    try {
        if (!std::filesystem::exists(filename)) {
            Log()->warn("配置文件不存在: {}", filename);
            return false;
        }
        
        std::ifstream file(filename);
        if (!file.is_open()) {
            Log()->error("无法打开配置文件: {}", filename);
            return false;
        }
        
        std::stringstream buffer;
        buffer << file.rdbuf();
        file.close();
        
        return loadFromJSON(buffer.str());
    }
    catch (const std::exception& e) {
        Log()->error("加载配置文件失败: {} - {}", filename, e.what());
        return false;
    }
}

bool Config::loadFromJSON(const std::string& jsonStr)
{
    try {
        _json = jsonStr;
        parseProxySettings();
        Log()->info("配置加载成功");
        return true;
    }
    catch (const std::exception& e) {
        Log()->error("解析JSON配置失败: {}", e.what());
        return false;
    }
}

std::optional<ProxySettings> Config::getProxySettings() const
{
    return _proxySettings;
}

void Config::setProxySettings(const ProxySettings& proxy)
{
    _proxySettings = proxy;
}

bool Config::saveToFile(const std::string& filename) const
{
    try {
        std::ofstream file(filename);
        if (!file.is_open()) {
            Log()->error("无法创建配置文件: {}", filename);
            return false;
        }
        
        file << toJSON();
        file.close();
        
        Log()->info("配置保存成功: {}", filename);
        return true;
    }
    catch (const std::exception& e) {
        Log()->error("保存配置文件失败: {} - {}", filename, e.what());
        return false;
    }
}

std::string Config::toJSON() const
{
    // 创建配置JSON
    std::ostringstream oss;
    oss << "{\n";
    oss << "  \"proxy\": {\n";
    
    if (_proxySettings.has_value()) {
        const auto& proxy = _proxySettings.value();
        oss << "    \"enabled\": " << (proxy.enabled ? "true" : "false") << ",\n";
        oss << "    \"host\": \"" << proxy.host << "\",\n";
        oss << "    \"port\": " << proxy.port;
        if (!proxy.username.empty()) {
            oss << ",\n    \"username\": \"" << proxy.username << "\"";
            if (!proxy.password.empty()) {
                oss << ",\n    \"password\": \"" << proxy.password << "\"";
            }
        }
        oss << "\n";
    } else {
        oss << "    \"enabled\": false,\n";
        oss << "    \"host\": \"127.0.0.1\",\n";
        oss << "    \"port\": 10809\n";
    }
    
    oss << "  }\n";
    oss << "}\n";
    
    return oss.str();
}

bool Config::createDefaultConfig(const std::string& filename)
{
    try {
        Config defaultConfig;
        // 设置默认代理配置
        ProxySettings defaultProxy;
        defaultProxy.enabled = true;  // 默认启用代理
        defaultProxy.host = "127.0.0.1";
        defaultProxy.port = 10809;
        defaultConfig.setProxySettings(defaultProxy);
        
        return defaultConfig.saveToFile(filename);
    }
    catch (const std::exception& e) {
        Log()->error("创建默认配置文件失败: {} - {}", filename, e.what());
        return false;
    }
}

void Config::parseProxySettings()
{
    try {
        // 简单的JSON解析（这里使用基本的字符串查找，实际项目中应该使用JSON库）
        if (_json.find("\"proxy\"") != std::string::npos) {
            ProxySettings proxy;
            
            // 解析enabled
            size_t enabledPos = _json.find("\"enabled\"");
            if (enabledPos != std::string::npos) {
                size_t colonPos = _json.find(":", enabledPos);
                if (colonPos != std::string::npos) {
                    size_t truePos = _json.find("true", colonPos);
                    size_t falsePos = _json.find("false", colonPos);
                    if (truePos != std::string::npos && (falsePos == std::string::npos || truePos < falsePos)) {
                        proxy.enabled = true;
                    } else {
                        proxy.enabled = false;
                    }
                }
            }
            
            // 解析host
            size_t hostPos = _json.find("\"host\"");
            if (hostPos != std::string::npos) {
                size_t colonPos = _json.find(":", hostPos);
                if (colonPos != std::string::npos) {
                    size_t quoteStart = _json.find("\"", colonPos);
                    if (quoteStart != std::string::npos) {
                        size_t quoteEnd = _json.find("\"", quoteStart + 1);
                        if (quoteEnd != std::string::npos) {
                            proxy.host = _json.substr(quoteStart + 1, quoteEnd - quoteStart - 1);
                        }
                    }
                }
            }
            
            // 解析port
            size_t portPos = _json.find("\"port\"");
            if (portPos != std::string::npos) {
                size_t colonPos = _json.find(":", portPos);
                if (colonPos != std::string::npos) {
                    size_t numStart = colonPos + 1;
                    while (numStart < _json.length() && (_json[numStart] == ' ' || _json[numStart] == '\t')) {
                        numStart++;
                    }
                    size_t numEnd = numStart;
                    while (numEnd < _json.length() && std::isdigit(_json[numEnd])) {
                        numEnd++;
                    }
                    if (numEnd > numStart) {
                        proxy.port = std::stoi(_json.substr(numStart, numEnd - numStart));
                    }
                }
            }
            
            _proxySettings = proxy;
            Log()->info("代理设置解析完成: {}:{} ({})", proxy.host, proxy.port, proxy.enabled ? "启用" : "禁用");
        }
    }
    catch (const std::exception& e) {
        Log()->error("解析代理设置失败: {}", e.what());
    }
}

// GlobalConfig implementation
GlobalConfig& GlobalConfig::instance()
{
    static GlobalConfig instance;
    return instance;
}

bool GlobalConfig::initialize()
{
    // 默认配置文件路径
    std::string configPath = "rocky_config.json";
    return initialize(configPath);
}

bool GlobalConfig::initialize(const std::string& configFile)
{
    _configFilePath = configFile;
    
    // 如果配置文件不存在，创建默认配置
    if (!std::filesystem::exists(configFile)) {
        Log()->info("配置文件不存在，创建默认配置: {}", configFile);
        if (!Config::createDefaultConfig(configFile)) {
            Log()->error("创建默认配置文件失败");
            return false;
        }
    }
    
    // 加载配置
    if (_config.loadFromFile(configFile)) {
        _initialized = true;
        Log()->info("全局配置初始化成功: {}", configFile);
        return true;
    } else {
        Log()->error("全局配置初始化失败: {}", configFile);
        return false;
    }
}

std::optional<ProxySettings> GlobalConfig::getProxySettings() const
{
    if (_initialized) {
        return _config.getProxySettings();
    }
    return std::nullopt;
}

std::string GlobalConfig::getConfigFilePath() const
{
    return _configFilePath;
}
