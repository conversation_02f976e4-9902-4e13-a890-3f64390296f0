{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/vsgxchange-x64-windows-1.1.4#1-b9926385-6684-43c3-af54-13ae4880acb5", "name": "vsgxchange:x64-windows@1.1.4#1 a5b00178c92ae304b8539570ee175898c37d6e3f96776b82be05c32ddefc5c6f", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-02T09:24:29Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "vsgxchange", "SPDXID": "SPDXRef-port", "versionInfo": "1.1.4#1", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/vsgxchange", "homepage": "https://github.com/vsg-dev/vsgXchange", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "Utility library for converting 3rd party images, models and fonts formats to/from VulkanSceneGraph.", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "vsgxchange:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "a5b00178c92ae304b8539570ee175898c37d6e3f96776b82be05c32ddefc5c6f", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "vsg-dev/vsgXchange", "downloadLocation": "git+https://github.com/vsg-dev/vsgXchange@v1.1.4", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "a4c79092162c64f745556fa64b10fd06906526f5f7b7e22e61fc34f42d50116fe1816ff5cb0ca862f7da6a4a221818e99867e8520da4ffc2b9867ef15a01cd13"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "c12731044e8c26b4f8717d8c0758d4ad313bc2e354c9207c41b63e9f19e3ae86"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "74ec46685ed9d84322265596739cd803aabd2f7a9a3fddefd0fca79e09c299ac"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}