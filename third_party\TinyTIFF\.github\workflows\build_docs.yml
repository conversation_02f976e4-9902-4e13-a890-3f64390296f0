# This is a basic workflow to help you get started with Actions

name: Doxygen build&deploy
 
# Controls when the workflow will run
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches: [ master ]

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-20.04

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - name: checkout
        uses: actions/checkout@v4
            
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get update --fix-missing
          sudo apt-get install -f
          sudo apt -y install build-essential doxygen graphviz doxygen-latex libclang-common-10-dev libclang-10-dev	fonts-freefont-ttf
          doxygen -v
      
      - name: Update Doxygen to 1.9.8
        run: |
          currDir=`pwd`
          cd $GITHUB_WORKSPACE
          mkdir doxygen
          wget -O - https://github.com/doxygen/doxygen/releases/download/Release_1_9_8/doxygen-1.9.8.linux.bin.tar.gz | tar --strip-components=1 -xz -C doxygen
          export PATH=$GITHUB_WORKSPACE/doxygen/bin:${PATH}
          echo PATH=$PATH
          cd $currDir
          $GITHUB_WORKSPACE/doxygen/bin/doxygen --version

      - name: Run Doxygen
        run: |
          $GITHUB_WORKSPACE/doxygen/bin/doxygen --version
          cd doc
          pwd
          ls
          $GITHUB_WORKSPACE/doxygen/bin/doxygen 
          pwd
          ls
          ls -R
      
      - name: Create .nojekyll (ensures pages with underscores work on gh pages)      
        run: |
            pwd
            touch doc/html/.nojekyll
        shell: bash

      - name: 🚀 Deploy to Github-Pages
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          #github_token: ${{ secrets.GH_PAGES_SECRET }}
          ssh-key: ${{ secrets.TINYTIFF_DEPLOY_KEY }}
          branch: gh-pages
          folder: doc/html
          
