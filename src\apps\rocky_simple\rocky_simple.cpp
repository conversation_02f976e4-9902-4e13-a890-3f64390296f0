/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */

#include <rocky/rocky.h>
#include <rocky/Utils.h> // for getExecutableLocation
#include <stdlib.h>      // for _putenv
#include <filesystem>
#include <vsg/ui/ScrollWheelEvent.h>

int main(int argc, char **argv)
{
    // Set the PROJ_DATA environment variable to point to our local data directory.
    // This MUST be done before any rocky operation that might initialize PROJ.
    std::filesystem::path exe_path = rocky::util::getExecutableLocation();
    std::filesystem::path proj_path = exe_path.parent_path() / "share" / "proj";
    _putenv_s("PROJ_DATA", proj_path.string().c_str());

    // Create the application object.
    rocky::Application app(argc, argv);

    // Force the application to create its window and graphics context.
    // This is necessary so that we can get a handle to the view and manipulator
    // that the application creates by default.
    app.realize();

    // Get a handle to the default manipulator and apply some settings.
    if (app.displayManager && !app.displayManager->windowsAndViews.empty())
    {
        // The Application set up a default window, view, and manipulator for us.
        auto &views = app.displayManager->windowsAndViews.begin()->second;
        if (!views.empty())
        {
            auto view = views.front();
            if (auto manip = rocky::MapManipulator::get(view))
            {
                // Create a new settings object by copying the existing one.
                auto new_settings = std::make_shared<rocky::MapManipulator::Settings>(*manip->getSettings());

                // Re-bind the scroll wheel to zoom in on scroll-up, and out on scroll-down.
                new_settings->bindScroll(
                    rocky::MapManipulator::ACTION_ZOOM_IN,
                    1); // Assumes 1 is SCROLL_UP

                new_settings->bindScroll(
                    rocky::MapManipulator::ACTION_ZOOM_OUT,
                    2); // Assumes 2 is SCROLL_DOWN

                // Set a minimum distance to the ground to prevent clipping.
                new_settings->minDistance = 15.0;

                // Apply the new settings.
                manip->applySettings(new_settings);
            }
        }
    }

    // Set the screen space error for the terrain engine. This value controls
    // the quality of the terrain mesh. A lower value means higher quality
    // (more detail) at the cost of more data downloaded and rendered.
    // A good default is around 1.5 to 2.0.
    app.mapNode->terrainSettings().screenSpaceError = 1.75f;

    // Create a TMS image layer that points to a spherical mercator data source.
    auto layer = rocky::TMSImageLayer::create();

    // The URI for the data source:
    layer->uri = "http://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";

    // A string to display for attribution:
    layer->attribution = rocky::Hyperlink{"\u00a9 Google", "https://google.com/copyright"};

    // This data source doesn't report any metadata, so we need to tell Rocky
    // about its tiling profile. Most online data sources use "spherical-mercator".
    layer->profile = rocky::Profile("spherical-mercator");

    // Add the layer to the map.
    app.mapNode->map->add(layer);

    // Run the application's main loop.
    return app.run();
}
