# SRS测试程序 - 不依赖VSG渲染器
add_executable(rocky_srs_test rocky_srs_test.cpp)
target_link_libraries(rocky_srs_test PRIVATE rocky)
set_property(TARGET rocky_srs_test PROPERTY FOLDER "Apps")

if(ROCKY_RENDERER_VSG)
    add_subdirectory(rocky_simple)
    add_subdirectory(rocky_engine)

    if(ROCKY_SUPPORTS_IMGUI)
        add_subdirectory(rocky_demo)
    endif()

    if(ROCKY_SUPPORTS_QT)
        add_subdirectory(rocky_demo_qt)
    endif()
endif()
