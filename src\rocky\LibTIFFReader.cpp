/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#include "LibTIFFReader.h"
#include "Log.h"

// Include LibTIFF headers
extern "C"
{
#include <tiffio.h>
}

#include <sstream>
#include <vector>
#include <cstring>
#include <algorithm>

using namespace ROCKY_NAMESPACE;
using namespace ROCKY_NAMESPACE::LibTIFFSupport;

#undef LC
#define LC "[LibTIFF] "

// Internal memory stream structure for libtiff callbacks
struct LibTIFFReader::MemoryStream
{
    const unsigned char* data;
    size_t size;
    size_t position;
    
    MemoryStream(const unsigned char* d, size_t s) 
        : data(d), size(s), position(0) {}
};

// LibTIFF callback functions for memory-based reading
tsize_t LibTIFFReader::memoryReadProc(thandle_t handle, tdata_t data, tsize_t size)
{
    MemoryStream* stream = static_cast<MemoryStream*>(handle);
    size_t availableBytes = stream->size - stream->position;
    size_t bytesToRead = std::min(static_cast<size_t>(size), availableBytes);
    
    if (bytesToRead > 0)
    {
        memcpy(data, stream->data + stream->position, bytesToRead);
        stream->position += bytesToRead;
    }
    
    return static_cast<tsize_t>(bytesToRead);
}

tsize_t LibTIFFReader::memoryWriteProc(thandle_t handle, tdata_t data, tsize_t size)
{
    // Read-only stream, writing not supported
    return 0;
}

toff_t LibTIFFReader::memorySeekProc(thandle_t handle, toff_t offset, int whence)
{
    MemoryStream* stream = static_cast<MemoryStream*>(handle);
    
    switch (whence)
    {
    case SEEK_SET:
        stream->position = static_cast<size_t>(offset);
        break;
    case SEEK_CUR:
        stream->position += static_cast<size_t>(offset);
        break;
    case SEEK_END:
        stream->position = stream->size + static_cast<size_t>(offset);
        break;
    default:
        return static_cast<toff_t>(-1);
    }
    
    if (stream->position > stream->size)
        stream->position = stream->size;
        
    return static_cast<toff_t>(stream->position);
}

int LibTIFFReader::memoryCloseProc(thandle_t handle)
{
    // Memory stream doesn't need closing
    return 0;
}

toff_t LibTIFFReader::memorySizeProc(thandle_t handle)
{
    MemoryStream* stream = static_cast<MemoryStream*>(handle);
    return static_cast<toff_t>(stream->size);
}

int LibTIFFReader::memoryMapProc(thandle_t handle, tdata_t* data, toff_t* size)
{
    MemoryStream* stream = static_cast<MemoryStream*>(handle);
    *data = const_cast<unsigned char*>(stream->data);
    *size = static_cast<toff_t>(stream->size);
    return 1;
}

void LibTIFFReader::memoryUnmapProc(thandle_t handle, tdata_t data, toff_t size)
{
    // Memory mapping doesn't need cleanup for read-only data
}

bool LibTIFFReader::isTIFFFormat(const unsigned char* data, size_t length)
{
    if (length < 4)
        return false;
        
    // Check TIFF magic numbers
    // Little-endian: 49 49 2A 00 (II*)
    // Big-endian: 4D 4D 00 2A (MM*)
    return (data[0] == 0x49 && data[1] == 0x49 && data[2] == 0x2A && data[3] == 0x00) ||
           (data[0] == 0x4D && data[1] == 0x4D && data[2] == 0x00 && data[3] == 0x2A);
}

Result<std::shared_ptr<Image>> LibTIFFReader::readFromMemory(
    const unsigned char* data, size_t length)
{
    if (!data || length == 0)
        return Status(Status::ConfigurationError, LC "Invalid input data");
        
    if (!isTIFFFormat(data, length))
        return Status(Status::ConfigurationError, LC "Not a valid TIFF format");
    
    // Create memory stream for zero-copy reading
    auto memoryStream = std::make_unique<MemoryStream>(data, length);
    
    // Open TIFF using LibTIFF with custom memory callbacks
    TIFF* tiff = TIFFClientOpen(
        "memory", "r",
        static_cast<thandle_t>(memoryStream.get()),
        memoryReadProc,
        memoryWriteProc,
        memorySeekProc,
        memoryCloseProc,
        memorySizeProc,
        memoryMapProc,
        memoryUnmapProc
    );
        
    if (!tiff)
    {
        return Status(Status::ResourceUnavailable, LC "Failed to open TIFF from memory");
    }
    
    // Read TIFF data
    Result<std::shared_ptr<Image>> result = readElevationTIFF(tiff);
    
    // Close TIFF
    TIFFClose(tiff);
    
    return result;
}

Result<std::shared_ptr<Image>> LibTIFFReader::readElevationTIFF(TIFF* tiff)
{
    // Get image dimensions and properties
    uint32 width, height;
    uint16 bitsPerSample, samplesPerPixel, sampleFormat;
    
    TIFFGetField(tiff, TIFFTAG_IMAGEWIDTH, &width);
    TIFFGetField(tiff, TIFFTAG_IMAGELENGTH, &height);
    TIFFGetField(tiff, TIFFTAG_BITSPERSAMPLE, &bitsPerSample);
    TIFFGetField(tiff, TIFFTAG_SAMPLESPERPIXEL, &samplesPerPixel);
    
    // Default to unsigned integer if not specified
    if (!TIFFGetField(tiff, TIFFTAG_SAMPLEFORMAT, &sampleFormat))
        sampleFormat = SAMPLEFORMAT_UINT;
    
    Log()->info(LC "TIFF dimensions: {}x{}, {} bits/sample, {} samples/pixel, format: {}", 
                width, height, bitsPerSample, samplesPerPixel, sampleFormat);
    
    std::shared_ptr<Image> image;
    
    // Handle elevation data formats
    if (bitsPerSample == 32 && samplesPerPixel == 1 && sampleFormat == SAMPLEFORMAT_IEEEFP)
    {
        // 32-bit float elevation data (most common for elevation)
        image = Image::create(Image::R32_SFLOAT, width, height);
        if (image)
        {
            float* pixels = image->data<float>();
            
            // Read scanlines
            for (uint32 row = 0; row < height; ++row)
            {
                if (TIFFReadScanline(tiff, pixels + (row * width), row, 0) < 0)
                {
                    return Status(Status::ResourceUnavailable, LC "Failed to read 32-bit float scanline");
                }
            }
        }
    }
    else if (bitsPerSample == 16 && samplesPerPixel == 1)
    {
        // 16-bit elevation data
        image = Image::create(Image::R16_UNORM, width, height);
        if (image)
        {
            uint16* pixels = image->data<uint16>();
            
            // Read scanlines
            for (uint32 row = 0; row < height; ++row)
            {
                if (TIFFReadScanline(tiff, pixels + (row * width), row, 0) < 0)
                {
                    return Status(Status::ResourceUnavailable, LC "Failed to read 16-bit scanline");
                }
            }
        }
    }
    else
    {
        return Status(Status::ConfigurationError, 
                     LC "Unsupported TIFF format for elevation data: " + std::to_string(bitsPerSample) + 
                     " bits/sample, " + std::to_string(samplesPerPixel) + " samples/pixel, format: " + 
                     std::to_string(sampleFormat));
    }
    
    if (!image)
        return Status(Status::ResourceUnavailable, LC "Failed to create image");
        
    return image;
}

Result<std::shared_ptr<Image>> LibTIFFReader::readFromStream(std::istream& stream)
{
    // Read entire stream into memory buffer (optimized for network streams)
    std::ostringstream buffer;
    buffer << stream.rdbuf();
    std::string data = buffer.str();
    
    if (data.empty())
        return Status(Status::ConfigurationError, LC "Empty input stream");
        
    return readFromMemory(
        reinterpret_cast<const unsigned char*>(data.c_str()), 
        data.size());
}

// VSG ReaderWriter implementation
LibTIFF_VSG_ReaderWriter::LibTIFF_VSG_ReaderWriter()
{
}

bool LibTIFF_VSG_ReaderWriter::canRead(const std::string& extension) const
{
    return extension == ".tif" || extension == ".tiff";
}

Result<std::shared_ptr<Image>> LibTIFF_VSG_ReaderWriter::read(
    std::istream& in, const std::string& extension) const
{
    if (!canRead(extension))
        return Status(Status::ConfigurationError, LC "Unsupported extension: " + extension);
        
    return LibTIFFReader::readFromStream(in);
}
