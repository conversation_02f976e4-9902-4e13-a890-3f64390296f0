cmake_minimum_required(VERSION 3.20)
project(simple_srs_test)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

if(MSVC)
    add_compile_options(/utf-8)
endif()

set(ROCKY_INCLUDE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/include")
set(ROCKY_LIB_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/lib")
set(ROCKY_BIN_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../redist_desk/bin")

add_executable(simple_srs_test simple_srs_test.cpp)
target_include_directories(simple_srs_test PRIVATE ${ROCKY_INCLUDE_DIR})
target_link_libraries(simple_srs_test PRIVATE "${ROCKY_LIB_DIR}/rocky.lib")

if(WIN32)
    add_custom_command(TARGET simple_srs_test POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${ROCKY_BIN_DIR}/rocky.dll"
        $<TARGET_FILE_DIR:simple_srs_test>)
        
    add_custom_command(TARGET simple_srs_test POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "C:/dev/vcpkg/installed/x64-windows/bin/GeographicLib.dll"
        $<TARGET_FILE_DIR:simple_srs_test>)
endif()
