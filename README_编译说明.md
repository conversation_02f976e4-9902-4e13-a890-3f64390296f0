# Rocky库编译说明

## 快速开始

### 一键编译
```bash
# 运行一键编译脚本
一键编译.bat
```

### 手动编译
```bash
# 1. 清理编译目录
build_clean.bat

# 2. 配置项目
build_config_core.bat

# 3. 编译项目
build_compile.bat
```

## 编译脚本说明

### 核心脚本
- `一键编译.bat` - 一键完成所有编译步骤
- `build_config_core.bat` - 配置项目（核心功能）
- `build_compile.bat` - 编译项目并复制文件
- `build_clean.bat` - 清理编译目录

### 其他脚本
- `build_config.bat` - 完整配置（包含Qt支持，目前有问题）
- `build_config_lite.bat` - 轻量配置（不包含ImGui）

## 编译配置

### 当前配置（轻量化）
- ✅ **ROCKY_SUPPORTS_HTTPLIB** - HTTP支持
- ✅ **ROCKY_SUPPORTS_GDAL** - 地理数据支持
- ✅ **ROCKY_RENDERER_VSG** - Vulkan渲染支持
- ❌ **ROCKY_SUPPORTS_QT** - Qt支持（已禁用）
- ❌ **ROCKY_SUPPORTS_IMGUI** - ImGui支持（已禁用）
- ❌ **ROCKY_SUPPORTS_CURL** - CURL支持（已禁用）
- ❌ **ROCKY_SUPPORTS_HTTPS** - HTTPS支持（已禁用）
- ❌ **ROCKY_SUPPORTS_MBTILES** - MBTiles支持（已禁用）

### 系统要求
- Windows 10/11 x64
- Visual Studio 2022
- vcpkg包管理器
- 支持Vulkan 1.1+的显卡驱动

### 目录结构
```
F:/rockyb2/                 # 编译目录
├── src/rocky/Release/      # 库文件
│   ├── rocky.dll
│   ├── rocky.lib
│   └── 其他依赖库...
├── src/apps/*/Release/     # 示例程序
│   ├── rocky_simple.exe
│   └── rocky_engine.exe
└── redist_desk/            # 发布目录
    ├── rocky.dll
    ├── rocky.lib
    ├── rocky_simple.exe
    ├── rocky_engine.exe
    └── 所有依赖DLL文件...
```

## 编译成果

### 主要文件
- `rocky.dll` - 核心动态链接库 (2.4MB)
- `rocky.lib` - 静态链接库 (952KB)
- `rocky_simple.exe` - 简单示例应用 (21KB)
- `rocky_engine.exe` - 引擎示例应用 (95KB)

### 依赖库 (总计约55MB)
- **图形渲染**: vsg-14.dll (6.8MB), vulkan-1.dll (730KB)
- **地理数据**: gdal.dll (21MB), proj_9.dll (3.4MB)
- **空间数据**: spatialite.dll (7.6MB), geos.dll (2.6MB)
- **图像处理**: 各种图像格式支持库
- **工具库**: spdlog.dll, fmt.dll, json-c.dll等

## 使用说明
详细使用方法请参考：`Rocky库使用说明.md`

## 问题记录
编译过程中的问题和解决方案请参考：`question.md`

## 注意事项

1. **编译环境**: 必须使用Visual Studio 2022和C++17标准
2. **字符编码**: 所有源代码使用UTF-8编码
3. **依赖管理**: 通过vcpkg管理第三方库依赖
4. **轻量化**: 当前配置已禁用非必要功能以减小体积

## 故障排除

### 常见问题
1. **CMake配置失败**: 检查vcpkg工具链路径
2. **编译错误**: 检查Visual Studio 2022是否正确安装
3. **依赖库缺失**: 运行`vcpkg install`更新依赖

### 联系支持
如遇到编译问题，请参考问题记录文档或查看原始项目文档。

---
*更新时间: 2024年*
*编译器: Visual Studio 2022*
*C++标准: C++17* 