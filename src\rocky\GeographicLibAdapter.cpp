/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#include "GeographicLibAdapter.h"
#include <algorithm>
#include <cctype>
#include <sstream>

using namespace ROCKY_NAMESPACE;

std::shared_ptr<GeographicLibAdapter::TransformHandle>
GeographicLibAdapter::createTransform(const std::string &fromDef, const std::string &toDef)
{
    auto handle = std::make_shared<TransformHandle>();

    // 解析源坐标系
    if (!parseDefinition(fromDef, *handle))
    {
        handle->lastError = "Failed to parse source definition: " + fromDef;
        return nullptr;
    }
    handle->fromType = handle->toType; // 临时存储

    // 解析目标坐标系
    if (!parseDefinition(toDef, *handle))
    {
        handle->lastError = "Failed to parse target definition: " + toDef;
        return nullptr;
    }
    // 现在toType是正确的，需要恢复fromType
    ProjectionType targetType = handle->toType;
    if (!parseDefinition(fromDef, *handle))
    {
        return nullptr;
    }
    handle->fromType = handle->toType;
    handle->toType = targetType;

    return handle;
}

bool GeographicLibAdapter::parseDefinition(const std::string &definition, TransformHandle &handle)
{
    std::string def = definition;
    std::transform(def.begin(), def.end(), def.begin(), ::tolower);

    // 清除空格
    def.erase(std::remove_if(def.begin(), def.end(), ::isspace), def.end());

    try
    {
        // WGS84地理坐标
        if (def.find("wgs84") != std::string::npos ||
            def.find("epsg:4326") != std::string::npos ||
            def.find("longlat") != std::string::npos)
        {
            handle.toType = ProjectionType::GEOGRAPHIC;
            return true;
        }

        // ECEF地心坐标
        if (def.find("ecef") != std::string::npos ||
            def.find("geocentric") != std::string::npos ||
            def.find("epsg:4978") != std::string::npos)
        {
            handle.toType = ProjectionType::GEOCENTRIC;
            return true;
        }

        // 球面墨卡托 (Web Mercator)
        if (def.find("spherical-mercator") != std::string::npos ||
            def.find("webmercator") != std::string::npos ||
            def.find("epsg:3857") != std::string::npos ||
            def.find("epsg:900913") != std::string::npos)
        {
            handle.toType = ProjectionType::MERCATOR;
            handle.centralMeridian = 0.0;
            handle.falseEasting = 0.0;
            handle.falseNorthing = 0.0;
            return true;
        }

        // UTM投影
        if (def.find("utm") != std::string::npos)
        {
            handle.toType = ProjectionType::UTM;

            // 解析UTM区域
            size_t zonePos = def.find("zone=");
            if (zonePos != std::string::npos)
            {
                std::string zoneStr = def.substr(zonePos + 5);
                size_t endPos = zoneStr.find_first_not_of("0123456789");
                if (endPos != std::string::npos)
                    zoneStr = zoneStr.substr(0, endPos);
                handle.utmZone = std::stoi(zoneStr);
            }

            // 判断南北半球
            handle.utmNorth = def.find("south") == std::string::npos;

            return true;
        }

        // 极地立体投影
        if (def.find("stere") != std::string::npos || def.find("polar") != std::string::npos)
        {
            handle.toType = ProjectionType::POLAR_STEREO;
            return true;
        }

        // 默认为地理坐标
        handle.toType = ProjectionType::GEOGRAPHIC;
        return true;
    }
    catch (const std::exception &e)
    {
        handle.lastError = "Parse error: " + std::string(e.what());
        return false;
    }
}

bool GeographicLibAdapter::forward(
    std::shared_ptr<TransformHandle> handle,
    double &x, double &y, double &z)
{
    if (!handle)
        return false;

    try
    {
        // 如果源和目标相同，直接返回
        if (handle->fromType == handle->toType)
            return true;

        // 先转换到地理坐标（如果需要）
        if (handle->fromType != ProjectionType::GEOGRAPHIC)
        {
            if (!projToGeo(*handle, x, y, z))
                return false;
        }

        // 再转换到目标坐标系（如果需要）
        if (handle->toType != ProjectionType::GEOGRAPHIC)
        {
            if (!geoToProj(*handle, x, y, z))
                return false;
        }

        return true;
    }
    catch (const std::exception &e)
    {
        handle->lastError = "Transform error: " + std::string(e.what());
        return false;
    }
}

bool GeographicLibAdapter::inverse(
    std::shared_ptr<TransformHandle> handle,
    double &x, double &y, double &z)
{
    if (!handle)
        return false;

    try
    {
        // 反向转换：交换源和目标
        ProjectionType originalFrom = handle->fromType;
        ProjectionType originalTo = handle->toType;

        handle->fromType = originalTo;
        handle->toType = originalFrom;

        bool result = forward(handle, x, y, z);

        // 恢复原始设置
        handle->fromType = originalFrom;
        handle->toType = originalTo;

        return result;
    }
    catch (const std::exception &e)
    {
        handle->lastError = "Inverse transform error: " + std::string(e.what());
        return false;
    }
}

bool GeographicLibAdapter::geoToProj(const TransformHandle &handle, double &x, double &y, double &z)
{
    double lon = x, lat = y, height = z;

    switch (handle.toType)
    {
    case ProjectionType::GEOCENTRIC:
        return geoToGeocentric(x, y, z);

    case ProjectionType::UTM:
    {
        // 简化的UTM投影（仅作为占位符）
        // 实际应用中需要完整的UTM实现
        x = x * 111320.0; // 简单的度到米转换
        y = y * 110540.0;
        return true;
    }

    case ProjectionType::MERCATOR:
    {
        // Web Mercator投影
        const double R = GeographicConstants::WGS84_a; // 地球半径
        x = R * lon * GeographicConstants::DEG_TO_RAD;
        y = R * std::log(std::tan(GeographicConstants::PI / 4.0 + lat * GeographicConstants::DEG_TO_RAD / 2.0));
        return true;
    }

    case ProjectionType::LOCAL_CARTESIAN:
    {
        // 简化的本地笛卡尔投影
        x = (x - handle.localOriginLon) * 111320.0;
        y = (y - handle.localOriginLat) * 110540.0;
        z = z - handle.localOriginHeight;
        return true;
    }

    default:
        return false;
    }
}

bool GeographicLibAdapter::projToGeo(const TransformHandle &handle, double &x, double &y, double &z)
{
    switch (handle.fromType)
    {
    case ProjectionType::GEOCENTRIC:
        return geocentricToGeo(x, y, z);

    case ProjectionType::UTM:
    {
        // 简化的UTM反投影
        x = x / 111320.0; // 简单的米到度转换
        y = y / 110540.0;
        return true;
    }

    case ProjectionType::MERCATOR:
    {
        // Web Mercator反投影
        const double R = GeographicConstants::WGS84_a;
        double lon = x / (R * GeographicConstants::DEG_TO_RAD);
        double lat = (2.0 * std::atan(std::exp(y / R)) - GeographicConstants::PI / 2.0) / GeographicConstants::DEG_TO_RAD;
        x = lon;
        y = lat;
        return true;
    }

    case ProjectionType::LOCAL_CARTESIAN:
    {
        // 简化的本地笛卡尔反投影
        x = x / 111320.0 + handle.localOriginLon;
        y = y / 110540.0 + handle.localOriginLat;
        z = z + handle.localOriginHeight;
        return true;
    }

    default:
        return false;
    }
}

bool GeographicLibAdapter::geoToGeocentric(double &x, double &y, double &z)
{
    // 简化的大地坐标到地心坐标转换
    double lon = x * GeographicConstants::DEG_TO_RAD;
    double lat = y * GeographicConstants::DEG_TO_RAD;
    double height = z;

    double a = GeographicConstants::WGS84_a;
    double b = GeographicConstants::WGS84_b;
    double e2 = 1.0 - (b * b) / (a * a);
    double N = a / std::sqrt(1.0 - e2 * std::sin(lat) * std::sin(lat));

    x = (N + height) * std::cos(lat) * std::cos(lon);
    y = (N + height) * std::cos(lat) * std::sin(lon);
    z = (N * (1.0 - e2) + height) * std::sin(lat);
    return true;
}

bool GeographicLibAdapter::geocentricToGeo(double &x, double &y, double &z)
{
    // 简化的地心坐标到大地坐标转换
    double X = x, Y = y, Z = z;

    double a = GeographicConstants::WGS84_a;
    double b = GeographicConstants::WGS84_b;
    double e2 = 1.0 - (b * b) / (a * a);

    double p = std::sqrt(X * X + Y * Y);
    double theta = std::atan2(Z * a, p * b);

    double lon = std::atan2(Y, X);
    double lat = std::atan2(Z + e2 * b * std::pow(std::sin(theta), 3),
                            p - e2 * a * std::pow(std::cos(theta), 3));
    double N = a / std::sqrt(1.0 - e2 * std::sin(lat) * std::sin(lat));
    double height = p / std::cos(lat) - N;

    x = lon * GeographicConstants::RAD_TO_DEG;
    y = lat * GeographicConstants::RAD_TO_DEG;
    z = height;
    return true;
}

std::string GeographicLibAdapter::version()
{
    return "GeographicLib Adapter 1.0 (Simplified)";
}

bool GeographicLibAdapter::forwardArray(
    std::shared_ptr<TransformHandle> handle,
    double *x, double *y, double *z,
    std::size_t stride, std::size_t count)
{
    if (!handle || !x || !y || !z)
        return false;

    bool allSuccess = true;
    for (std::size_t i = 0; i < count; ++i)
    {
        double *px = reinterpret_cast<double *>(reinterpret_cast<char *>(x) + i * stride);
        double *py = reinterpret_cast<double *>(reinterpret_cast<char *>(y) + i * stride);
        double *pz = reinterpret_cast<double *>(reinterpret_cast<char *>(z) + i * stride);

        if (!forward(handle, *px, *py, *pz))
            allSuccess = false;
    }
    return allSuccess;
}

bool GeographicLibAdapter::inverseArray(
    std::shared_ptr<TransformHandle> handle,
    double *x, double *y, double *z,
    std::size_t stride, std::size_t count)
{
    if (!handle || !x || !y || !z)
        return false;

    bool allSuccess = true;
    for (std::size_t i = 0; i < count; ++i)
    {
        double *px = reinterpret_cast<double *>(reinterpret_cast<char *>(x) + i * stride);
        double *py = reinterpret_cast<double *>(reinterpret_cast<char *>(y) + i * stride);
        double *pz = reinterpret_cast<double *>(reinterpret_cast<char *>(z) + i * stride);

        if (!inverse(handle, *px, *py, *pz))
            allSuccess = false;
    }
    return allSuccess;
}

std::string GeographicLibAdapter::getError(std::shared_ptr<TransformHandle> handle)
{
    return handle ? handle->lastError : "Invalid handle";
}
