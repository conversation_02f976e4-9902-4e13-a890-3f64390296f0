/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#include "GeographicLibAdapter.h"
#include <GeographicLib/Constants.hpp>
#include <algorithm>
#include <cctype>
#include <sstream>

using namespace ROCKY_NAMESPACE;

std::shared_ptr<GeographicLibAdapter::TransformHandle>
GeographicLibAdapter::createTransform(const std::string &fromDef, const std::string &toDef)
{
    auto handle = std::make_shared<TransformHandle>();

    // 解析源坐标系
    if (!parseDefinition(fromDef, *handle))
    {
        handle->lastError = "Failed to parse source definition: " + fromDef;
        return nullptr;
    }
    handle->fromType = handle->toType; // 临时存储

    // 解析目标坐标系
    if (!parseDefinition(toDef, *handle))
    {
        handle->lastError = "Failed to parse target definition: " + toDef;
        return nullptr;
    }
    // 现在toType是正确的，需要恢复fromType
    ProjectionType targetType = handle->toType;
    if (!parseDefinition(fromDef, *handle))
    {
        return nullptr;
    }
    handle->fromType = handle->toType;
    handle->toType = targetType;

    return handle;
}

bool GeographicLibAdapter::parseDefinition(const std::string &definition, TransformHandle &handle)
{
    std::string def = definition;
    std::transform(def.begin(), def.end(), def.begin(), ::tolower);

    // 清除空格
    def.erase(std::remove_if(def.begin(), def.end(), ::isspace), def.end());

    try
    {
        // WGS84地理坐标
        if (def.find("wgs84") != std::string::npos ||
            def.find("epsg:4326") != std::string::npos ||
            def.find("longlat") != std::string::npos)
        {
            handle.toType = ProjectionType::GEOGRAPHIC;
            return true;
        }

        // ECEF地心坐标
        if (def.find("ecef") != std::string::npos ||
            def.find("geocentric") != std::string::npos ||
            def.find("epsg:4978") != std::string::npos)
        {
            handle.toType = ProjectionType::GEOCENTRIC;
            return true;
        }

        // 球面墨卡托 (Web Mercator)
        if (def.find("spherical-mercator") != std::string::npos ||
            def.find("webmercator") != std::string::npos ||
            def.find("epsg:3857") != std::string::npos ||
            def.find("epsg:900913") != std::string::npos)
        {
            handle.toType = ProjectionType::MERCATOR;
            handle.centralMeridian = 0.0;
            handle.falseEasting = 0.0;
            handle.falseNorthing = 0.0;
            return true;
        }

        // UTM投影
        if (def.find("utm") != std::string::npos)
        {
            handle.toType = ProjectionType::UTM;

            // 解析UTM区域
            size_t zonePos = def.find("zone=");
            if (zonePos != std::string::npos)
            {
                std::string zoneStr = def.substr(zonePos + 5);
                size_t endPos = zoneStr.find_first_not_of("0123456789");
                if (endPos != std::string::npos)
                    zoneStr = zoneStr.substr(0, endPos);
                handle.utmZone = std::stoi(zoneStr);
            }

            // 判断南北半球
            handle.utmNorth = def.find("south") == std::string::npos;

            return true;
        }

        // 极地立体投影
        if (def.find("stere") != std::string::npos || def.find("polar") != std::string::npos)
        {
            handle.toType = ProjectionType::POLAR_STEREO;
            return true;
        }

        // 默认为地理坐标
        handle.toType = ProjectionType::GEOGRAPHIC;
        return true;
    }
    catch (const std::exception &e)
    {
        handle.lastError = "Parse error: " + std::string(e.what());
        return false;
    }
}

bool GeographicLibAdapter::forward(
    std::shared_ptr<TransformHandle> handle,
    double &x, double &y, double &z)
{
    if (!handle)
        return false;

    try
    {
        // 如果源和目标相同，直接返回
        if (handle->fromType == handle->toType)
            return true;

        // 先转换到地理坐标（如果需要）
        if (handle->fromType != ProjectionType::GEOGRAPHIC)
        {
            if (!projToGeo(*handle, x, y, z))
                return false;
        }

        // 再转换到目标坐标系（如果需要）
        if (handle->toType != ProjectionType::GEOGRAPHIC)
        {
            if (!geoToProj(*handle, x, y, z))
                return false;
        }

        return true;
    }
    catch (const std::exception &e)
    {
        handle->lastError = "Transform error: " + std::string(e.what());
        return false;
    }
}

bool GeographicLibAdapter::inverse(
    std::shared_ptr<TransformHandle> handle,
    double &x, double &y, double &z)
{
    if (!handle)
        return false;

    try
    {
        // 反向转换：交换源和目标
        ProjectionType originalFrom = handle->fromType;
        ProjectionType originalTo = handle->toType;

        handle->fromType = originalTo;
        handle->toType = originalFrom;

        bool result = forward(handle, x, y, z);

        // 恢复原始设置
        handle->fromType = originalFrom;
        handle->toType = originalTo;

        return result;
    }
    catch (const std::exception &e)
    {
        handle->lastError = "Inverse transform error: " + std::string(e.what());
        return false;
    }
}

bool GeographicLibAdapter::geoToProj(const TransformHandle &handle, double &x, double &y, double &z)
{
    double lon = x, lat = y, height = z;

    switch (handle.toType)
    {
    case ProjectionType::GEOCENTRIC:
        return geoToGeocentric(x, y, z);

    case ProjectionType::UTM:
    {
        int zone;
        bool northp;
        double utmX, utmY;
        double gamma, k;

        GeographicLib::UTMUPS::Forward(lat, lon, zone, northp, utmX, utmY, gamma, k);
        x = utmX;
        y = utmY;
        return true;
    }

    case ProjectionType::MERCATOR:
    {
        // Web Mercator投影
        const double R = GeographicLib::Constants::WGS84_a(); // 地球半径
        x = R * lon * GeographicLib::Math::degree();
        y = R * std::log(std::tan(GeographicLib::Math::pi() / 4.0 + lat * GeographicLib::Math::degree() / 2.0));
        return true;
    }

    case ProjectionType::LOCAL_CARTESIAN:
    {
        if (!handle.localCartesian)
        {
            handle.localCartesian = std::make_unique<GeographicLib::LocalCartesian>(
                handle.localOriginLat, handle.localOriginLon, handle.localOriginHeight);
        }
        handle.localCartesian->Forward(lat, lon, height, x, y, z);
        return true;
    }

    default:
        return false;
    }
}

bool GeographicLibAdapter::projToGeo(const TransformHandle &handle, double &x, double &y, double &z)
{
    switch (handle.fromType)
    {
    case ProjectionType::GEOCENTRIC:
        return geocentricToGeo(x, y, z);

    case ProjectionType::UTM:
    {
        int zone = handle.utmZone;
        bool northp = handle.utmNorth;
        double lat, lon;
        double gamma, k;

        GeographicLib::UTMUPS::Reverse(zone, northp, x, y, lat, lon, gamma, k);
        x = lon;
        y = lat;
        return true;
    }

    case ProjectionType::MERCATOR:
    {
        // Web Mercator反投影
        const double R = GeographicLib::Constants::WGS84_a();
        double lon = x / (R * GeographicLib::Math::degree());
        double lat = (2.0 * std::atan(std::exp(y / R)) - GeographicLib::Math::pi() / 2.0) / GeographicLib::Math::degree();
        x = lon;
        y = lat;
        return true;
    }

    case ProjectionType::LOCAL_CARTESIAN:
    {
        if (!handle.localCartesian)
        {
            handle.localCartesian = std::make_unique<GeographicLib::LocalCartesian>(
                handle.localOriginLat, handle.localOriginLon, handle.localOriginHeight);
        }
        double lat, lon, height;
        handle.localCartesian->Reverse(x, y, z, lat, lon, height);
        x = lon;
        y = lat;
        z = height;
        return true;
    }

    default:
        return false;
    }
}

bool GeographicLibAdapter::geoToGeocentric(double &x, double &y, double &z)
{
    double lon = x, lat = y, height = z;
    GeographicLib::Geocentric earth(GeographicLib::Constants::WGS84_a(), GeographicLib::Constants::WGS84_f());
    earth.Forward(lat, lon, height, x, y, z);
    return true;
}

bool GeographicLibAdapter::geocentricToGeo(double &x, double &y, double &z)
{
    double X = x, Y = y, Z = z;
    GeographicLib::Geocentric earth(GeographicLib::Constants::WGS84_a(), GeographicLib::Constants::WGS84_f());
    earth.Reverse(X, Y, Z, x, y, z);
    return true;
}

std::string GeographicLibAdapter::version()
{
    return "GeographicLib " + std::to_string(GEOGRAPHICLIB_VERSION_MAJOR) + "." +
           std::to_string(GEOGRAPHICLIB_VERSION_MINOR) + "." +
           std::to_string(GEOGRAPHICLIB_VERSION_PATCH);
}

bool GeographicLibAdapter::forwardArray(
    std::shared_ptr<TransformHandle> handle,
    double *x, double *y, double *z,
    std::size_t stride, std::size_t count)
{
    if (!handle || !x || !y || !z)
        return false;

    bool allSuccess = true;
    for (std::size_t i = 0; i < count; ++i)
    {
        double *px = reinterpret_cast<double *>(reinterpret_cast<char *>(x) + i * stride);
        double *py = reinterpret_cast<double *>(reinterpret_cast<char *>(y) + i * stride);
        double *pz = reinterpret_cast<double *>(reinterpret_cast<char *>(z) + i * stride);

        if (!forward(handle, *px, *py, *pz))
            allSuccess = false;
    }
    return allSuccess;
}

bool GeographicLibAdapter::inverseArray(
    std::shared_ptr<TransformHandle> handle,
    double *x, double *y, double *z,
    std::size_t stride, std::size_t count)
{
    if (!handle || !x || !y || !z)
        return false;

    bool allSuccess = true;
    for (std::size_t i = 0; i < count; ++i)
    {
        double *px = reinterpret_cast<double *>(reinterpret_cast<char *>(x) + i * stride);
        double *py = reinterpret_cast<double *>(reinterpret_cast<char *>(y) + i * stride);
        double *pz = reinterpret_cast<double *>(reinterpret_cast<char *>(z) + i * stride);

        if (!inverse(handle, *px, *py, *pz))
            allSuccess = false;
    }
    return allSuccess;
}

std::string GeographicLibAdapter::getError(std::shared_ptr<TransformHandle> handle)
{
    return handle ? handle->lastError : "Invalid handle";
}
