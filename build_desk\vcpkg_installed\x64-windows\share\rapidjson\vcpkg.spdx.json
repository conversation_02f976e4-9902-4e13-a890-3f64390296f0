{"$schema": "https://raw.githubusercontent.com/spdx/spdx-spec/v2.2.1/schemas/spdx-schema.json", "spdxVersion": "SPDX-2.2", "dataLicense": "CC0-1.0", "SPDXID": "SPDXRef-DOCUMENT", "documentNamespace": "https://spdx.org/spdxdocs/rapidjson-x64-windows-2025-02-26-09df0dca-cfa2-430d-aad4-bdd84719c950", "name": "rapidjson:x64-windows@2025-02-26 997ff4aaf2fa8128ed8dd50b3fb2d4e4a22711fa7033bceb3436d4246829b807", "creationInfo": {"creators": ["Tool: vcpkg-2025-06-20-ef7c0d541124bbdd334a03467e7edb6c3364d199"], "created": "2025-07-02T09:14:18Z"}, "relationships": [{"spdxElementId": "SPDXRef-port", "relationshipType": "GENERATES", "relatedSpdxElement": "SPDXRef-binary"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-0"}, {"spdxElementId": "SPDXRef-port", "relationshipType": "CONTAINS", "relatedSpdxElement": "SPDXRef-file-1"}, {"spdxElementId": "SPDXRef-binary", "relationshipType": "GENERATED_FROM", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-0", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "CONTAINED_BY", "relatedSpdxElement": "SPDXRef-port"}, {"spdxElementId": "SPDXRef-file-1", "relationshipType": "DEPENDENCY_MANIFEST_OF", "relatedSpdxElement": "SPDXRef-port"}], "packages": [{"name": "<PERSON><PERSON><PERSON>", "SPDXID": "SPDXRef-port", "versionInfo": "2025-02-26", "downloadLocation": "git+https://github.com/Microsoft/vcpkg#ports/rapidjson", "homepage": "http://rapidjson.org/", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "description": "A fast JSON parser/generator for C++ with both SAX/DOM style API <http://rapidjson.org/>", "comment": "This is the port (recipe) consumed by vcpkg."}, {"name": "rapidjson:x64-windows", "SPDXID": "SPDXRef-binary", "versionInfo": "997ff4aaf2fa8128ed8dd50b3fb2d4e4a22711fa7033bceb3436d4246829b807", "downloadLocation": "NONE", "licenseConcluded": "MIT", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "comment": "This is a binary package built by vcpkg."}, {"SPDXID": "SPDXRef-resource-0", "name": "Tencent/rapidjson", "downloadLocation": "git+https://github.com/Tencent/rapidjson@24b5e7a8b27f42fa16b96fc70aade9106cf7102f", "licenseConcluded": "NOASSERTION", "licenseDeclared": "NOASSERTION", "copyrightText": "NOASSERTION", "checksums": [{"algorithm": "SHA512", "checksumValue": "50f8723414a6e63eadd45f97be5c44e9fff2d06216c8cc4df802f5bfc2a9416a039f2c69e9bb1882f7e756cd38a7097eea05cab76c739f45805dc41617140799"}]}], "files": [{"fileName": "./portfile.cmake", "SPDXID": "SPDXRef-file-0", "checksums": [{"algorithm": "SHA256", "checksumValue": "7dfa011f5f7b7834ba3b5365869982488bdefe0931ac2b7bacc661e47895244e"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}, {"fileName": "./vcpkg.json", "SPDXID": "SPDXRef-file-1", "checksums": [{"algorithm": "SHA256", "checksumValue": "9d217e7f19c78ffb509e17ba5c1a175fff4602658d3fe6b23a06049ff8219ba5"}], "licenseConcluded": "NOASSERTION", "copyrightText": "NOASSERTION"}]}