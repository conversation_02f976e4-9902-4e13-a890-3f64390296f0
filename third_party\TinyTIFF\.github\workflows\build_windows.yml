name: BUILD-MSVC-CI

on: [push, pull_request]

permissions:
  checks: write
  
jobs:
  ci:
    if: >-
      ! contains(toJSON(github.event.commits.*.message), '[skip ci]') &&
      ! contains(toJSON(github.event.commits.*.message), '[skip github]')
    name: MSVC 2022
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - {gen: Visual Studio 17 2022, arch: Win32, shared: ON}
          - {gen: Visual Studio 17 2022, arch: Win32, shared: OFF}
          - {gen: Visual Studio 17 2022, arch: x64, shared: ON}
          - {gen: Visual Studio 17 2022, arch: x64, shared: OFF}
    steps:
    - name: checkout
      uses: actions/checkout@v4
    - name: Configure
      run: |
        mkdir install
        cmake -G "${{matrix.gen}}" -A ${{matrix.arch}} -DBUILD_SHARED_LIBS=${{matrix.shared}} "-DCMAKE_INSTALL_PREFIX=./install" -B build
    - name: Build Release
      run: |
           cmake --build build --config Release --verbose           
    - name: Install Release
      run: |
        cmake --install build --config Release
        cd install
        ls -R
    - name: Test CMake-build against TinyTIFF
      run: |
        cd tests
        cd extcmake_tinytiff_test
        mkdir build
        cd build
        pwd
        echo ${{github.workspace}}
        cmake -G "${{matrix.gen}}" -A ${{matrix.arch}} "-DCMAKE_PREFIX_PATH=${{github.workspace}}/install/" -B . -S ..
        cmake --build . --config Release --verbose  
    - name: Test CMake-build against TinyTIFF, using FetchContent-API
      if: success() || failure() # always run even if the previous step fails
      run: |
        cd tests
        cd extcmake_fetchcontent_tinytiff_test
        mkdir build 
        cd build
        cmake -G "${{matrix.gen}}" -A ${{matrix.arch}} -B . -S ..
        cmake --build . --config Release --verbose  

    - name: Run Release tests
      run: |
        cd install
        cd bin
        pwd
        ls 
        .\tinytiffwriter_test --simple
        .\tinytiffreader_test --simple
    - name: Publish Test Report
      uses: mikepenz/action-junit-report@v4
      if: success() || failure() # always run even if the previous step fails
      with:
        report_paths: '**/install/bin/*.xml'
