/*
Open Asset Import Library (assimp)
----------------------------------------------------------------------

Copyright (c) 2006-2025, assimp team

All rights reserved.

Redistribution and use of this software in source and binary forms,
with or without modification, are permitted provided that the
following conditions are met:

* Redistributions of source code must retain the above
  copyright notice, this list of conditions and the
  following disclaimer.

* Redistributions in binary form must reproduce the above
  copyright notice, this list of conditions and the
  following disclaimer in the documentation and/or other
  materials provided with the distribution.

* Neither the name of the assimp team, nor the names of its
  contributors may be used to endorse or promote products
  derived from this software without specific prior
  written permission of the assimp team.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
"AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
(INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

----------------------------------------------------------------------
*/

/** @file CreateAnimMesh.h
 *  Create AnimMesh from Mesh
 */
#pragma once
#ifndef INCLUDED_AI_CREATE_ANIM_MESH_H
#define INCLUDED_AI_CREATE_ANIM_MESH_H

#ifdef __GNUC__
#   pragma GCC system_header
#endif

#include <assimp/mesh.h>

namespace Assimp {

/**
 *  Create aiAnimMesh from aiMesh.
 *  @param  mesh            The input mesh to create an animated mesh from.
 *  @param  needPositions   If true, positions will be copied from.
 *  @param  needNormals     If true, normals will be copied from.
 *  @param  needTangents    If true, tangents and bitangents will be copied from.
 *  @param  needColors      If true, colors will be copied from.
 *  @param  needTexCoords   If true, texCoords will be copied from.
 *  @return The new created animated mesh.
 */
ASSIMP_API aiAnimMesh *aiCreateAnimMesh(const aiMesh *mesh,
                                        bool needPositions = true,
                                        bool needNormals = true,
                                        bool needTangents = true,
                                        bool needColors = true,
                                        bool needTexCoords = true);

} // end of namespace Assimp

#endif // INCLUDED_AI_CREATE_ANIM_MESH_H

