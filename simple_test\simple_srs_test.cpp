#include <rocky/SRS.h>
#include <rocky/GeoPoint.h>
#include <iostream>
#include <iomanip>
#include <cmath>

using namespace rocky;

int main() {
    std::cout << "Rocky SRS 基本功能验证" << std::endl;
    
    try {
        // 测试1: 创建基本坐标系
        std::cout << "\n1. 创建坐标系测试:" << std::endl;
        SRS wgs84("epsg:4326");
        SRS webMercator("epsg:3857");
        
        std::cout << "WGS84: " << (wgs84.valid() ? "✅" : "❌") << std::endl;
        std::cout << "Web Mercator: " << (webMercator.valid() ? "✅" : "❌") << std::endl;
        
        if (!wgs84.valid() || !webMercator.valid()) {
            std::cout << "❌ 基本坐标系创建失败" << std::endl;
            return 1;
        }
        
        // 测试2: 关键坐标转换
        std::cout << "\n2. 关键坐标转换测试:" << std::endl;
        
        // 测试原点转换
        GeoPoint origin(wgs84, 0.0, 0.0, 0.0);
        GeoPoint mercatorOrigin = origin.transform(webMercator);
        
        std::cout << std::fixed << std::setprecision(6);
        std::cout << "原点 (0°, 0°) -> (" << mercatorOrigin.x << ", " << mercatorOrigin.y << ")" << std::endl;
        
        if (std::abs(mercatorOrigin.x) < 1.0 && std::abs(mercatorOrigin.y) < 1.0) {
            std::cout << "✅ 原点转换正确" << std::endl;
        } else {
            std::cout << "❌ 原点转换错误" << std::endl;
        }
        
        // 测试边界点转换
        GeoPoint westBoundary(wgs84, -180.0, 0.0, 0.0);
        GeoPoint mercatorWest = westBoundary.transform(webMercator);
        
        std::cout << "西边界 (-180°, 0°) -> (" << mercatorWest.x << ", " << mercatorWest.y << ")" << std::endl;
        
        double expectedWestX = -20037508.342789244;
        if (std::abs(mercatorWest.x - expectedWestX) < 1.0) {
            std::cout << "✅ 西边界转换正确" << std::endl;
        } else {
            std::cout << "❌ 西边界转换错误，期望: " << expectedWestX << std::endl;
        }
        
        // 测试3: 往返转换精度
        std::cout << "\n3. 往返转换精度测试:" << std::endl;
        
        GeoPoint beijing(wgs84, 116.4074, 39.9042, 0.0);
        GeoPoint beijingMercator = beijing.transform(webMercator);
        GeoPoint beijingBack = beijingMercator.transform(wgs84);
        
        double lonError = std::abs(beijingBack.x - 116.4074);
        double latError = std::abs(beijingBack.y - 39.9042);
        
        std::cout << "北京往返转换误差: 经度=" << std::scientific << std::setprecision(2) 
                  << lonError << "°, 纬度=" << latError << "°" << std::endl;
        
        if (lonError < 1e-9 && latError < 1e-9) {
            std::cout << "✅ 往返转换精度合格" << std::endl;
        } else {
            std::cout << "❌ 往返转换精度不足" << std::endl;
        }
        
        // 测试4: 坐标系属性
        std::cout << "\n4. 坐标系属性测试:" << std::endl;
        
        std::cout << "WGS84 - 地理坐标系: " << (wgs84.isGeodetic() ? "✅" : "❌") << std::endl;
        std::cout << "WGS84 - 投影坐标系: " << (wgs84.isProjected() ? "❌" : "✅") << std::endl;
        std::cout << "Web Mercator - 地理坐标系: " << (webMercator.isGeodetic() ? "❌" : "✅") << std::endl;
        std::cout << "Web Mercator - 投影坐标系: " << (webMercator.isProjected() ? "✅" : "❌") << std::endl;
        
        // 测试5: 椭球体参数
        std::cout << "\n5. 椭球体参数测试:" << std::endl;
        
        const Ellipsoid& ellipsoid = wgs84.ellipsoid();
        double semiMajor = ellipsoid.semiMajorAxis();
        double semiMinor = ellipsoid.semiMinorAxis();
        
        std::cout << std::fixed << std::setprecision(3);
        std::cout << "WGS84长半轴: " << semiMajor << " 米" << std::endl;
        std::cout << "WGS84短半轴: " << semiMinor << " 米" << std::endl;
        
        // 验证WGS84椭球体参数
        if (std::abs(semiMajor - 6378137.0) < 1.0) {
            std::cout << "✅ 椭球体长半轴正确" << std::endl;
        } else {
            std::cout << "❌ 椭球体长半轴错误，期望: 6378137.0" << std::endl;
        }
        
        std::cout << "\n=== 验证总结 ===" << std::endl;
        std::cout << "✅ Rocky SRS重构基本功能正常" << std::endl;
        std::cout << "✅ GeographicLib适配器工作正常" << std::endl;
        std::cout << "✅ 关键坐标转换功能验证通过" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ 验证过程中发生异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "❌ 验证过程中发生未知异常" << std::endl;
        return 1;
    }
}
