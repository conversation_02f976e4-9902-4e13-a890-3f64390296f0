#!/usr/bin/env python3
"""
深入研究ReadyMap高程数据的实际编码方式
通过分析已知地理位置来推断正确的解码公式
"""

import struct
import numpy as np
from pathlib import Path
import requests

def download_known_location_tile():
    """下载一个已知地理位置的瓦片进行分析"""
    
    # 选择一个已知的地理位置：美国大峡谷地区
    # 大峡谷的高程大约在600-2100米之间
    # Web Mercator坐标大约在 z=8, x=52, y=99 附近
    
    z, x, y = 8, 52, 99
    url = f"https://readymap.org/readymap/tiles/1.0.0/116/{z}/{x}/{y}.tif"
    
    print(f"下载已知位置瓦片: 美国大峡谷地区")
    print(f"瓦片坐标: z={z}, x={x}, y={y}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        data = response.content
        print(f"文件大小: {len(data)} bytes")
        
        # 保存文件
        cache_dir = Path("cache/elevation")
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        filename = f"grand_canyon_z{z}_x{x}_y{y}.tif"
        filepath = cache_dir / filename
        
        with open(filepath, 'wb') as f:
            f.write(data)
        
        print(f"已保存到: {filepath}")
        return filepath, data
        
    except Exception as e:
        print(f"下载失败: {e}")
        return None, None

def analyze_raw_data_patterns(data):
    """分析原始数据的模式来推断编码方式"""
    
    print(f"\n=== 原始数据模式分析 ===")
    
    # 尝试不同的数据起始位置
    possible_starts = [
        len(data) - 257*257*2,  # 假设数据在文件末尾
        8,                      # 假设数据在TIFF头之后
        1024,                   # 假设有较大的头部
        len(data) // 2,         # 假设数据在文件中间
    ]
    
    for i, start_pos in enumerate(possible_starts):
        if start_pos < 0 or start_pos >= len(data):
            continue
            
        print(f"\n--- 尝试起始位置 {i+1}: {start_pos} ---")
        
        # 读取一些样本数据
        sample_size = min(1000, len(data) - start_pos)
        if sample_size < 100:
            continue
            
        sample_data = data[start_pos:start_pos + sample_size]
        
        # 解析为16位无符号整数
        num_values = sample_size // 2
        raw_values = struct.unpack('<' + 'H' * num_values, sample_data[:num_values*2])
        
        print(f"样本数据范围: {min(raw_values)} - {max(raw_values)}")
        print(f"样本平均值: {sum(raw_values)/len(raw_values):.1f}")
        
        # 尝试不同的转换公式
        test_formulas = [
            ("raw_value - 32768", lambda x: x - 32768),
            ("raw_value / 10 - 3276.8", lambda x: x / 10 - 3276.8),
            ("raw_value / 100", lambda x: x / 100),
            ("raw_value * 0.1 - 1000", lambda x: x * 0.1 - 1000),
            ("(raw_value - 32768) / 10", lambda x: (x - 32768) / 10),
            ("raw_value", lambda x: x),  # 直接使用原始值
        ]
        
        for formula_name, formula_func in test_formulas:
            converted = [formula_func(v) for v in raw_values[:100]]  # 只转换前100个值
            min_val, max_val = min(converted), max(converted)
            avg_val = sum(converted) / len(converted)
            
            # 检查是否在合理的高程范围内（对于大峡谷地区：600-2100米）
            reasonable = 0 <= min_val <= 3000 and 0 <= max_val <= 3000 and 0 <= avg_val <= 3000
            status = "✅" if reasonable else "❌"
            
            print(f"  {status} {formula_name}: {min_val:.1f} - {max_val:.1f} 米 (平均: {avg_val:.1f})")

def research_readymap_encoding():
    """研究ReadyMap的编码方式"""
    
    print("=== ReadyMap 高程数据编码研究 ===\n")
    
    # 首先下载一个已知位置的瓦片
    filepath, data = download_known_location_tile()
    
    if not data:
        print("无法下载测试数据，使用现有缓存文件")
        cache_dir = Path("cache/elevation")
        tiff_files = list(cache_dir.glob("*.tif"))
        if tiff_files:
            filepath = tiff_files[0]
            with open(filepath, 'rb') as f:
                data = f.read()
        else:
            print("❌ 没有可用的TIFF文件")
            return
    
    # 分析原始数据模式
    analyze_raw_data_patterns(data)
    
    # 尝试使用LibTIFF的方式解析
    print(f"\n=== 使用标准TIFF解析 ===")
    try:
        # 这里我们模拟LibTIFF的解析过程
        # 实际上Rocky中的LibTIFF会正确解析TIFF结构
        
        print("建议:")
        print("1. ReadyMap可能使用了特殊的高程编码方式")
        print("2. 需要查看ReadyMap的官方文档或API说明")
        print("3. 可能需要调整Rocky中的高程解析代码")
        print("4. 建议测试其他高程数据源进行对比")
        
        # 生成研究报告
        generate_research_report(filepath, data)
        
    except Exception as e:
        print(f"解析失败: {e}")

def generate_research_report(filepath, data):
    """生成研究报告"""
    
    analysis_dir = Path("cache/analysis")
    analysis_dir.mkdir(exist_ok=True)
    
    report_file = analysis_dir / "readymap_encoding_research.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== ReadyMap 高程数据编码研究报告 ===\n\n")
        f.write(f"研究文件: {filepath.name}\n")
        f.write(f"文件大小: {len(data)} bytes\n\n")
        
        f.write("发现的问题:\n")
        f.write("1. 使用 'elevation = raw_value - 32768' 公式得到的高程值不合理\n")
        f.write("2. 高程范围过大 (-32767 到 32766 米)\n")
        f.write("3. 平均高程为负值，不符合地理常识\n\n")
        
        f.write("可能的原因:\n")
        f.write("1. ReadyMap使用了不同的高程编码方式\n")
        f.write("2. 数据可能经过了压缩或特殊处理\n")
        f.write("3. 我们的TIFF解析方式可能不完整\n")
        f.write("4. ReadyMap的数据格式可能与标准DEM不同\n\n")
        
        f.write("建议的解决方案:\n")
        f.write("1. 查阅ReadyMap官方文档\n")
        f.write("2. 测试其他已知的高程数据源\n")
        f.write("3. 使用专业的GIS工具验证数据\n")
        f.write("4. 联系ReadyMap技术支持\n")
        f.write("5. 考虑使用其他高程数据源如AWS Terrarium\n\n")
        
        f.write("技术细节:\n")
        f.write("- TIFF格式: 16位无符号整数\n")
        f.write("- 瓦片尺寸: 257x257 像素\n")
        f.write("- 坐标系: Web Mercator\n")
        f.write("- 数据源: ReadyMap.org\n")
    
    print(f"\n研究报告已保存: {report_file}")

if __name__ == "__main__":
    research_readymap_encoding()
