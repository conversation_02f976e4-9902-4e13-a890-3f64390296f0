/**
 * rocky c++
 * Copyright 2023 Pelican Mapping
 * MIT License
 */
#pragma once

#include <rocky/Common.h>
#include <rocky/Image.h>
#include <rocky/Status.h>
#include <memory>
#include <istream>

// Forward declarations for LibTIFF types
typedef struct tiff TIFF;
typedef void *thandle_t;
typedef void *tdata_t;
typedef long tsize_t;
typedef unsigned long toff_t;

namespace ROCKY_NAMESPACE
{
    namespace LibTIFFSupport
    {
        /**
         * LibTIFF-based TIFF reader for Rocky
         * High-performance stream-based TIFF reading without temporary files
         */
        class ROCKY_EXPORT LibTIFFReader
        {
        public:
            /**
             * Read a TIFF image from a memory buffer (zero-copy when possible)
             * @param data Raw TIFF data
             * @param length Size of data in bytes
             * @return Image object or error status
             */
            static Result<std::shared_ptr<Image>> readFromMemory(
                const unsigned char *data,
                size_t length);

            /**
             * Read a TIFF image from a stream (optimized for network streams)
             * @param stream Input stream containing TIFF data
             * @return Image object or error status
             */
            static Result<std::shared_ptr<Image>> readFromStream(
                std::istream &stream);

            /**
             * Check if the data appears to be a valid TIFF file
             * @param data First few bytes of the file
             * @param length Number of bytes to check
             * @return true if appears to be TIFF format
             */
            static bool isTIFFFormat(const unsigned char *data, size_t length);

        private:
            // Internal memory stream structure for libtiff callbacks
            struct MemoryStream;

            // LibTIFF callback functions for memory-based reading
            static tsize_t memoryReadProc(thandle_t handle, tdata_t data, tsize_t size);
            static tsize_t memoryWriteProc(thandle_t handle, tdata_t data, tsize_t size);
            static toff_t memorySeekProc(thandle_t handle, toff_t offset, int whence);
            static int memoryCloseProc(thandle_t handle);
            static toff_t memorySizeProc(thandle_t handle);
            static int memoryMapProc(thandle_t handle, tdata_t *data, toff_t *size);
            static void memoryUnmapProc(thandle_t handle, tdata_t data, toff_t size);

            // Helper methods for different TIFF data types
            static Result<std::shared_ptr<Image>> readElevationTIFF(TIFF *tiff);
            static Result<std::shared_ptr<Image>> readImageTIFF(TIFF *tiff);
        };

        /**
         * VSG ReaderWriter implementation using LibTIFF
         * High-performance alternative to GDAL for TIFF support
         */
        class ROCKY_EXPORT LibTIFF_VSG_ReaderWriter
        {
        public:
            LibTIFF_VSG_ReaderWriter();

            bool canRead(const std::string &extension) const;

            Result<std::shared_ptr<Image>> read(
                std::istream &in,
                const std::string &extension) const;
        };
    }
}
